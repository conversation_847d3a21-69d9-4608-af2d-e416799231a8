# RequestContextService - Accessing Angular Headers in AppServices

## Overview

The `RequestContextService` is an injectable service that provides easy access to HTTP headers sent from Angular applications, specifically `LanguageCode` and `SpokenLangCode` headers.

## Features

- Access to `LanguageCode` header value
- Access to `SpokenLangCode` header value
- Convenient method to get both values at once
- Null-safe operations when headers are not present
- Scoped lifetime for proper request isolation

## Setup

The service is automatically registered in the dependency injection container through the `HolyBlessApplicationModule`. No additional configuration is required.

## Usage

### 1. Inject the Service

In any AppService, inject `IRequestContextService`:

```csharp
public class YourAppService : ApplicationService
{
    private readonly IRequestContextService _requestContextService;

    public YourAppService(IRequestContextService requestContextService)
    {
        _requestContextService = requestContextService;
    }
}
```

### 2. Access Individual Headers

```csharp
public async Task<string> SomeMethodAsync()
{
    // Get language code
    var languageCode = _requestContextService.GetLanguageCode();
    
    // Get spoken language code
    var spokenLangCode = _requestContextService.GetSpokenLangCode();
    
    // Use the values in your business logic
    if (!string.IsNullOrEmpty(languageCode))
    {
        // Apply language-specific logic
    }
    
    return "Success";
}
```

### 3. Get Both Values at Once

```csharp
public async Task ProcessRequestAsync()
{
    var (languageCode, spokenLangCode) = _requestContextService.GetLanguageCodes();
    
    // Use both values
    await ApplyLocalizationAsync(languageCode, spokenLangCode);
}
```

## Angular Integration

Ensure your Angular application sends the required headers with each API request:

```typescript
// In your Angular HTTP interceptor or service
const headers = new HttpHeaders({
  'LanguageCode': 'en-US',
  'SpokenLangCode': 'en'
});

this.http.get('/api/articles', { headers }).subscribe(response => {
  // Handle response
});
```

## Common Use Cases

### 1. Content Localization

```csharp
public async Task<List<ArticleDto>> GetLocalizedArticlesAsync()
{
    var languageCode = _requestContextService.GetLanguageCode();
    
    var queryable = await _repository.GetQueryableAsync();
    
    if (!string.IsNullOrEmpty(languageCode))
    {
        queryable = queryable.Where(a => a.Language == languageCode);
    }
    
    var articles = await AsyncExecuter.ToListAsync(queryable);
    return ObjectMapper.Map<List<Article>, List<ArticleDto>>(articles);
}
```

### 2. Audio Content Selection

```csharp
public async Task<AudioContentDto> GetAudioContentAsync(int contentId)
{
    var spokenLangCode = _requestContextService.GetSpokenLangCode();
    
    var content = await _repository.GetAsync(contentId);
    
    if (!string.IsNullOrEmpty(spokenLangCode))
    {
        // Select audio file based on spoken language
        content.AudioUrl = GetAudioUrlForLanguage(content.Id, spokenLangCode);
    }
    
    return ObjectMapper.Map<AudioContent, AudioContentDto>(content);
}
```

### 3. Creating Language-Aware Records

```csharp
public async Task<ArticleDto> CreateArticleAsync(CreateUpdateArticleDto input)
{
    var (languageCode, spokenLangCode) = _requestContextService.GetLanguageCodes();
    
    var article = ObjectMapper.Map<CreateUpdateArticleDto, Article>(input);
    
    // Set language properties based on headers
    article.ContentLanguage = languageCode ?? "en-US";
    article.AudioLanguage = spokenLangCode ?? "en";
    
    article = await _repository.InsertAsync(article, autoSave: true);
    return ObjectMapper.Map<Article, ArticleDto>(article);
}
```

## Error Handling

The service gracefully handles scenarios where:
- HTTP context is not available (returns null)
- Headers are not present (returns null)
- Headers have empty values (returns empty string)

Always check for null values before using the returned language codes:

```csharp
var languageCode = _requestContextService.GetLanguageCode();
if (!string.IsNullOrEmpty(languageCode))
{
    // Safe to use languageCode
}
```

## Testing

The service can be easily mocked for unit testing:

```csharp
[Fact]
public async Task SomeMethod_WithLanguageCode_ShouldProcessCorrectly()
{
    // Arrange
    var mockRequestContextService = new Mock<IRequestContextService>();
    mockRequestContextService.Setup(x => x.GetLanguageCode()).Returns("zh-CN");
    
    var appService = new YourAppService(mockRequestContextService.Object);
    
    // Act & Assert
    // Your test logic here
}
```

## Notes

- The service is registered with Scoped lifetime, ensuring proper isolation per request
- Header names are case-sensitive: `LanguageCode` and `SpokenLangCode`
- The service depends on `IHttpContextAccessor` which is automatically registered
- Works only in HTTP request context (not in background jobs or console applications)
