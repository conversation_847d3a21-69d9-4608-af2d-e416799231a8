using Volo.Abp.Application.Services;

namespace HolyBless.Services
{
    /// <summary>
    /// Service for accessing request context information like language codes from headers
    /// </summary>
    public interface IRequestContextService : IApplicationService
    {
        /// <summary>
        /// Gets the LanguageCode from the request header
        /// </summary>
        /// <returns>The language code or null if not present</returns>
        string? GetLanguageCode();

        /// <summary>
        /// Gets the SpokenLangCode from the request header
        /// </summary>
        /// <returns>The spoken language code or null if not present</returns>
        string? GetSpokenLangCode();

        /// <summary>
        /// Gets both language codes as a tuple
        /// </summary>
        /// <returns>Tuple containing (LanguageCode, SpokenLangCode)</returns>
        (string? LanguageCode, string? SpokenLangCode) GetLanguageCodes();
    }
}
