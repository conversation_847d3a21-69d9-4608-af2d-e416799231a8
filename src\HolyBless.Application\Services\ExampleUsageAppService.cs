using System.Threading.Tasks;
using HolyBless.Services;
using Microsoft.AspNetCore.Authorization;
using Volo.Abp.Application.Services;

namespace HolyBless.Examples
{
    /// <summary>
    /// Example showing how to use RequestContextService in your AppServices
    /// </summary>
    [Authorize]
    public class ExampleUsageAppService : ApplicationService
    {
        private readonly IRequestContextService _requestContextService;

        public ExampleUsageAppService(IRequestContextService requestContextService)
        {
            _requestContextService = requestContextService;
        }

        public async Task<string> GetUserLanguageInfoAsync()
        {
            // Get individual language codes
            var languageCode = _requestContextService.GetLanguageCode();
            var spokenLangCode = _requestContextService.GetSpokenLangCode();

            // Or get both at once
            var (langCode, spokenCode) = _requestContextService.GetLanguageCodes();

            // Use the language codes in your business logic
            var message = $"User's Language: {languageCode ?? "Not specified"}, " +
                         $"Spoken Language: {spokenLangCode ?? "Not specified"}";

            return await Task.FromResult(message);
        }

        public async Task<object> GetLocalizedContentAsync()
        {
            var languageCode = _requestContextService.GetLanguageCode();
            var spokenLangCode = _requestContextService.GetSpokenLangCode();

            // Example: Use language codes to filter content or apply localization
            // This is just an example - implement your actual business logic here

            if (!string.IsNullOrEmpty(languageCode))
            {
                // Filter content based on language code
                // e.g., query database with language filter
            }

            if (!string.IsNullOrEmpty(spokenLangCode))
            {
                // Apply spoken language specific logic
                // e.g., audio content selection
            }

            return new
            {
                LanguageCode = languageCode,
                SpokenLangCode = spokenLangCode,
                Message = "Content filtered based on language preferences"
            };
        }
    }
}