﻿using System;
using System.Collections.Generic;
using HolyBless.Entities.Albums;
using HolyBless.Entities.Collections;
using HolyBless.Entities.VirtualFolders;
using HolyBless.Enums;

namespace HolyBless.Entities.Buckets
{
    public class BucketFile : LanguageAuditedAggregateRoot<int>
    {
        public string FileName { get; set; } = default!;
        public string? Title { get; set; }  //In case wants to display different name on UI for FileName
        public string RelativePathInBucket { get; set; } = "";
        public MediaType MediaType { get; set; } = MediaType.Image; //Calculated based on file extension
        public ContentCategory ContentCategory { get; set; } = ContentCategory.Thumbnail;
        public DateTime? DeliveryDate { get; set; }
        public long? Size { get; set; }
        public int Views { get; set; } = 0;
        public string? YoutubeId { get; set; }
        public string? SpokenLangCode { get; set; }
        public string Environment { get; set; } = EnvironmentConst.Dev.ToString();
        public bool Exists { get; set; } = true;
        public DateTime? LastModifiedAtStorage { get; set; } //Last modified time at storage, not the database
        public ICollection<FolderToBucketFile> FolderToBucketFiles { get; set; } = [];

        public ICollection<CollectionToFile> CollectionToFiles { get; set; } = [];

        public ICollection<AlbumToFile> AlbumToFiles { get; set; } = [];

        public ICollection<BucketFileUrl> BucketFileUrls { get; set; } = [];

        public BucketFile()
        {
        }

        public BucketFile(int id) : base(id)
        {
        }
    }
}