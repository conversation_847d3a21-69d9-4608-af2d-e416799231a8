import type { AuditedEntityDto, EntityDto, PagedAndSortedResultRequestDto } from '@abp/ng.core';
import type { AlbumType } from '../../enums/album-type.enum';

export interface AlbumDto extends AuditedEntityDto<number> {
  channelId?: number;
  thumbnailFileId?: number;
  thumbnailUrl?: string;
  contentCode?: string;
  languageCode?: string;
  spokenLangCode?: string;
  title?: string;
  description?: string;
  views: number;
  likes: number;
  weight: number;
  albumType?: AlbumType;
}

export interface AlbumSearchDto extends PagedAndSortedResultRequestDto {
  channelId?: number;
  albumType?: AlbumType;
}

export interface AlbumToFileDto extends EntityDto {
  albumId: number;
  fileId: number;
  title?: string;
  weight: number;
  fileName?: string;
  fileUrl?: string;
  albumTitle?: string;
}
