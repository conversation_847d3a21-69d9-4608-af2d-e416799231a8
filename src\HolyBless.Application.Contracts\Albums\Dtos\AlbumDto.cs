using HolyBless.Enums;
using System.Collections.Generic;
using Volo.Abp.Application.Dtos;

namespace HolyBless.Albums.Dtos
{
    public class AlbumDto : AuditedEntityDto<int>
    {
        public int? ChannelId { get; set; }
        public string? ChannelName { get; set; }
        public string ChannelContentCode { get; set; } = "";
        public int? ThumbnailFileId { get; set; }
        public string? ThumbnailUrl { get; set; }
        public string ContentCode { get; set; } = "";
        public string? LanguageCode { get; set; }
        public string? SpokenLangCode { get; set; }
        public string Title { get; set; } = "";
        public string? Description { get; set; }
        public int Views { get; set; }
        public int Likes { get; set; }
        public int Weight { get; set; }
        public AlbumType AlbumType { get; set; }
    }
}
