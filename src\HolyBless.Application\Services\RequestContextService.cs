using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.DependencyInjection;
using Volo.Abp.Application.Services;
using Volo.Abp.DependencyInjection;

namespace HolyBless.Services
{
    /// <summary>
    /// Service for accessing request context information like language codes from headers
    /// </summary>
    [Dependency(ServiceLifetime.Scoped)]
    public class RequestContextService : ApplicationService, IRequestContextService
    {
        private readonly IHttpContextAccessor _httpContextAccessor;

        // Header names that <PERSON><PERSON> will send
        private const string LanguageCodeHeader = "LanguageCode";

        private const string SpokenLangCodeHeader = "SpokenLangCode";

        public RequestContextService(IHttpContextAccessor httpContextAccessor)
        {
            _httpContextAccessor = httpContextAccessor;
        }

        /// <summary>
        /// Gets the LanguageCode from the request header
        /// </summary>
        /// <returns>The language code or null if not present</returns>
        public string? GetLanguageCode()
        {
            var httpContext = _httpContextAccessor.HttpContext;
            if (httpContext?.Request?.Headers == null)
            {
                return null;
            }

            return httpContext.Request.Headers.TryGetValue(LanguageCodeHeader, out var languageCode)
                ? languageCode.ToString()
                : null;
        }

        /// <summary>
        /// Gets the SpokenLangCode from the request header
        /// </summary>
        /// <returns>The spoken language code or null if not present</returns>
        public string? GetSpokenLangCode()
        {
            var httpContext = _httpContextAccessor.HttpContext;
            if (httpContext?.Request?.Headers == null)
            {
                return null;
            }

            return httpContext.Request.Headers.TryGetValue(SpokenLangCodeHeader, out var spokenLangCode)
                ? spokenLangCode.ToString()
                : null;
        }

        /// <summary>
        /// Gets both language codes as a tuple
        /// </summary>
        /// <returns>Tuple containing (LanguageCode, SpokenLangCode)</returns>
        public (string? LanguageCode, string? SpokenLangCode) GetLanguageCodes()
        {
            return (GetLanguageCode(), GetSpokenLangCode());
        }
    }
}