{"generated": ["app"], "modules": {"abp": {"rootPath": "abp", "remoteServiceName": "abp", "controllers": {"Pages.Abp.MultiTenancy.AbpTenantController": {"controllerName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "controllerGroupName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "isRemoteService": true, "isIntegrationService": false, "apiVersion": null, "type": "Pages.Abp.MultiTenancy.AbpTenantController", "interfaces": [{"type": "Volo.Abp.AspNetCore.Mvc.MultiTenancy.IAbpTenantAppService", "name": "IAbpTenantAppService", "methods": [{"name": "FindTenantByNameAsync", "parametersOnMethod": [{"name": "name", "typeAsString": "System.String, System.Private.CoreLib", "type": "System.String", "typeSimple": "string", "isOptional": false, "defaultValue": null}], "returnValue": {"type": "Volo.Abp.AspNetCore.Mvc.MultiTenancy.FindTenantResultDto", "typeSimple": "Volo.Abp.AspNetCore.Mvc.MultiTenancy.FindTenantResultDto"}}, {"name": "FindTenantByIdAsync", "parametersOnMethod": [{"name": "id", "typeAsString": "System.Guid, System.Private.CoreLib", "type": "System.Guid", "typeSimple": "string", "isOptional": false, "defaultValue": null}], "returnValue": {"type": "Volo.Abp.AspNetCore.Mvc.MultiTenancy.FindTenantResultDto", "typeSimple": "Volo.Abp.AspNetCore.Mvc.MultiTenancy.FindTenantResultDto"}}]}], "actions": {"FindTenantByNameAsyncByName": {"uniqueName": "FindTenantByNameAsyncByName", "name": "FindTenantByNameAsync", "httpMethod": "GET", "url": "api/abp/multi-tenancy/tenants/by-name/{name}", "supportedVersions": [], "parametersOnMethod": [{"name": "name", "typeAsString": "System.String, System.Private.CoreLib", "type": "System.String", "typeSimple": "string", "isOptional": false, "defaultValue": null}], "parameters": [{"nameOnMethod": "name", "name": "name", "jsonName": null, "type": "System.String", "typeSimple": "string", "isOptional": false, "defaultValue": null, "constraintTypes": [], "bindingSourceId": "Path", "descriptorName": ""}], "returnValue": {"type": "Volo.Abp.AspNetCore.Mvc.MultiTenancy.FindTenantResultDto", "typeSimple": "Volo.Abp.AspNetCore.Mvc.MultiTenancy.FindTenantResultDto"}, "allowAnonymous": null, "implementFrom": "Volo.Abp.AspNetCore.Mvc.MultiTenancy.IAbpTenantAppService"}, "FindTenantByIdAsyncById": {"uniqueName": "FindTenantByIdAsyncById", "name": "FindTenantByIdAsync", "httpMethod": "GET", "url": "api/abp/multi-tenancy/tenants/by-id/{id}", "supportedVersions": [], "parametersOnMethod": [{"name": "id", "typeAsString": "System.Guid, System.Private.CoreLib", "type": "System.Guid", "typeSimple": "string", "isOptional": false, "defaultValue": null}], "parameters": [{"nameOnMethod": "id", "name": "id", "jsonName": null, "type": "System.Guid", "typeSimple": "string", "isOptional": false, "defaultValue": null, "constraintTypes": [], "bindingSourceId": "Path", "descriptorName": ""}], "returnValue": {"type": "Volo.Abp.AspNetCore.Mvc.MultiTenancy.FindTenantResultDto", "typeSimple": "Volo.Abp.AspNetCore.Mvc.MultiTenancy.FindTenantResultDto"}, "allowAnonymous": null, "implementFrom": "Volo.Abp.AspNetCore.Mvc.MultiTenancy.IAbpTenantAppService"}}}, "Volo.Abp.AspNetCore.Mvc.ApiExploring.AbpApiDefinitionController": {"controllerName": "AbpApiDefinition", "controllerGroupName": "AbpApiDefinition", "isRemoteService": true, "isIntegrationService": false, "apiVersion": null, "type": "Volo.Abp.AspNetCore.Mvc.ApiExploring.AbpApiDefinitionController", "interfaces": [], "actions": {"GetByModel": {"uniqueName": "GetByModel", "name": "Get", "httpMethod": "GET", "url": "api/abp/api-definition", "supportedVersions": [], "parametersOnMethod": [{"name": "model", "typeAsString": "Volo.Abp.Http.Modeling.ApplicationApiDescriptionModelRequestDto, Volo.Abp.Http", "type": "Volo.Abp.Http.Modeling.ApplicationApiDescriptionModelRequestDto", "typeSimple": "Volo.Abp.Http.Modeling.ApplicationApiDescriptionModelRequestDto", "isOptional": false, "defaultValue": null}], "parameters": [{"nameOnMethod": "model", "name": "IncludeTypes", "jsonName": null, "type": "System.Boolean", "typeSimple": "boolean", "isOptional": false, "defaultValue": null, "constraintTypes": null, "bindingSourceId": "ModelBinding", "descriptorName": "model"}], "returnValue": {"type": "Volo.Abp.Http.Modeling.ApplicationApiDescriptionModel", "typeSimple": "Volo.Abp.Http.Modeling.ApplicationApiDescriptionModel"}, "allowAnonymous": null, "implementFrom": "Volo.Abp.AspNetCore.Mvc.ApiExploring.AbpApiDefinitionController"}}}, "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.AbpApplicationConfigurationController": {"controllerName": "AbpApplicationConfiguration", "controllerGroupName": "AbpApplicationConfiguration", "isRemoteService": true, "isIntegrationService": false, "apiVersion": null, "type": "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.AbpApplicationConfigurationController", "interfaces": [{"type": "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.IAbpApplicationConfigurationAppService", "name": "IAbpApplicationConfigurationAppService", "methods": [{"name": "GetAsync", "parametersOnMethod": [{"name": "options", "typeAsString": "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ApplicationConfigurationRequestOptions, Volo.Abp.AspNetCore.Mvc.Contracts", "type": "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ApplicationConfigurationRequestOptions", "typeSimple": "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ApplicationConfigurationRequestOptions", "isOptional": false, "defaultValue": null}], "returnValue": {"type": "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ApplicationConfigurationDto", "typeSimple": "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ApplicationConfigurationDto"}}]}], "actions": {"GetAsyncByOptions": {"uniqueName": "GetAsyncByOptions", "name": "GetAsync", "httpMethod": "GET", "url": "api/abp/application-configuration", "supportedVersions": [], "parametersOnMethod": [{"name": "options", "typeAsString": "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ApplicationConfigurationRequestOptions, Volo.Abp.AspNetCore.Mvc.Contracts", "type": "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ApplicationConfigurationRequestOptions", "typeSimple": "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ApplicationConfigurationRequestOptions", "isOptional": false, "defaultValue": null}], "parameters": [{"nameOnMethod": "options", "name": "IncludeLocalizationResources", "jsonName": null, "type": "System.Boolean", "typeSimple": "boolean", "isOptional": false, "defaultValue": null, "constraintTypes": null, "bindingSourceId": "ModelBinding", "descriptorName": "options"}], "returnValue": {"type": "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ApplicationConfigurationDto", "typeSimple": "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ApplicationConfigurationDto"}, "allowAnonymous": null, "implementFrom": "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.IAbpApplicationConfigurationAppService"}}}, "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.AbpApplicationLocalizationController": {"controllerName": "AbpApplicationLocalization", "controllerGroupName": "AbpApplicationLocalization", "isRemoteService": true, "isIntegrationService": false, "apiVersion": null, "type": "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.AbpApplicationLocalizationController", "interfaces": [{"type": "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.IAbpApplicationLocalizationAppService", "name": "IAbpApplicationLocalizationAppService", "methods": [{"name": "GetAsync", "parametersOnMethod": [{"name": "input", "typeAsString": "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ApplicationLocalizationRequestDto, Volo.Abp.AspNetCore.Mvc.Contracts", "type": "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ApplicationLocalizationRequestDto", "typeSimple": "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ApplicationLocalizationRequestDto", "isOptional": false, "defaultValue": null}], "returnValue": {"type": "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ApplicationLocalizationDto", "typeSimple": "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ApplicationLocalizationDto"}}]}], "actions": {"GetAsyncByInput": {"uniqueName": "GetAsyncByInput", "name": "GetAsync", "httpMethod": "GET", "url": "api/abp/application-localization", "supportedVersions": [], "parametersOnMethod": [{"name": "input", "typeAsString": "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ApplicationLocalizationRequestDto, Volo.Abp.AspNetCore.Mvc.Contracts", "type": "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ApplicationLocalizationRequestDto", "typeSimple": "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ApplicationLocalizationRequestDto", "isOptional": false, "defaultValue": null}], "parameters": [{"nameOnMethod": "input", "name": "CultureName", "jsonName": null, "type": "System.String", "typeSimple": "string", "isOptional": false, "defaultValue": null, "constraintTypes": null, "bindingSourceId": "ModelBinding", "descriptorName": "input"}, {"nameOnMethod": "input", "name": "OnlyDynamics", "jsonName": null, "type": "System.Boolean", "typeSimple": "boolean", "isOptional": false, "defaultValue": null, "constraintTypes": null, "bindingSourceId": "ModelBinding", "descriptorName": "input"}], "returnValue": {"type": "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ApplicationLocalizationDto", "typeSimple": "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ApplicationLocalizationDto"}, "allowAnonymous": null, "implementFrom": "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.IAbpApplicationLocalizationAppService"}}}}}, "account": {"rootPath": "account", "remoteServiceName": "AbpAccount", "controllers": {"Volo.Abp.Account.Web.Areas.Account.Controllers.AccountController": {"controllerName": "Account", "controllerGroupName": "<PERSON><PERSON>", "isRemoteService": true, "isIntegrationService": false, "apiVersion": null, "type": "Volo.Abp.Account.Web.Areas.Account.Controllers.AccountController", "interfaces": [], "actions": {"LoginByLogin": {"uniqueName": "LoginByLogin", "name": "<PERSON><PERSON>", "httpMethod": "POST", "url": "api/account/login", "supportedVersions": [], "parametersOnMethod": [{"name": "login", "typeAsString": "Volo.Abp.Account.Web.Areas.Account.Controllers.Models.UserLoginInfo, Volo.Abp.Account.Web", "type": "Volo.Abp.Account.Web.Areas.Account.Controllers.Models.UserLoginInfo", "typeSimple": "Volo.Abp.Account.Web.Areas.Account.Controllers.Models.UserLoginInfo", "isOptional": false, "defaultValue": null}], "parameters": [{"nameOnMethod": "login", "name": "login", "jsonName": null, "type": "Volo.Abp.Account.Web.Areas.Account.Controllers.Models.UserLoginInfo", "typeSimple": "Volo.Abp.Account.Web.Areas.Account.Controllers.Models.UserLoginInfo", "isOptional": false, "defaultValue": null, "constraintTypes": null, "bindingSourceId": "Body", "descriptorName": ""}], "returnValue": {"type": "Volo.Abp.Account.Web.Areas.Account.Controllers.Models.AbpLoginResult", "typeSimple": "Volo.Abp.Account.Web.Areas.Account.Controllers.Models.AbpLoginResult"}, "allowAnonymous": null, "implementFrom": "Volo.Abp.Account.Web.Areas.Account.Controllers.AccountController"}, "Logout": {"uniqueName": "Logout", "name": "Logout", "httpMethod": "GET", "url": "api/account/logout", "supportedVersions": [], "parametersOnMethod": [], "parameters": [], "returnValue": {"type": "System.Void", "typeSimple": "System.Void"}, "allowAnonymous": null, "implementFrom": "Volo.Abp.Account.Web.Areas.Account.Controllers.AccountController"}, "CheckPasswordByLogin": {"uniqueName": "CheckPasswordByLogin", "name": "CheckPassword", "httpMethod": "POST", "url": "api/account/check-password", "supportedVersions": [], "parametersOnMethod": [{"name": "login", "typeAsString": "Volo.Abp.Account.Web.Areas.Account.Controllers.Models.UserLoginInfo, Volo.Abp.Account.Web", "type": "Volo.Abp.Account.Web.Areas.Account.Controllers.Models.UserLoginInfo", "typeSimple": "Volo.Abp.Account.Web.Areas.Account.Controllers.Models.UserLoginInfo", "isOptional": false, "defaultValue": null}], "parameters": [{"nameOnMethod": "login", "name": "login", "jsonName": null, "type": "Volo.Abp.Account.Web.Areas.Account.Controllers.Models.UserLoginInfo", "typeSimple": "Volo.Abp.Account.Web.Areas.Account.Controllers.Models.UserLoginInfo", "isOptional": false, "defaultValue": null, "constraintTypes": null, "bindingSourceId": "Body", "descriptorName": ""}], "returnValue": {"type": "Volo.Abp.Account.Web.Areas.Account.Controllers.Models.AbpLoginResult", "typeSimple": "Volo.Abp.Account.Web.Areas.Account.Controllers.Models.AbpLoginResult"}, "allowAnonymous": null, "implementFrom": "Volo.Abp.Account.Web.Areas.Account.Controllers.AccountController"}}}}}, "app": {"rootPath": "app", "remoteServiceName": "<PERSON><PERSON><PERSON>", "controllers": {"HolyBless.Albums.ReadOnlyAlbumAppService": {"controllerName": "ReadOnlyAlbum", "controllerGroupName": "ReadOnlyAlbum", "isRemoteService": true, "isIntegrationService": false, "apiVersion": null, "type": "HolyBless.Albums.ReadOnlyAlbumAppService", "interfaces": [{"type": "HolyBless.Albums.IReadOnlyAlbumAppService", "name": "IReadOnlyAlbumAppService", "methods": [{"name": "GetAsync", "parametersOnMethod": [{"name": "id", "typeAsString": "System.Int32, System.Private.CoreLib", "type": "System.Int32", "typeSimple": "number", "isOptional": false, "defaultValue": null}], "returnValue": {"type": "HolyBless.Albums.Dtos.AlbumDto", "typeSimple": "HolyBless.Albums.Dtos.AlbumDto"}}, {"name": "GetListAsync", "parametersOnMethod": [{"name": "input", "typeAsString": "HolyBless.Albums.Dtos.AlbumSearchDto, HolyBless.Application.Contracts", "type": "HolyBless.Albums.Dtos.AlbumSearchDto", "typeSimple": "HolyBless.Albums.Dtos.AlbumSearchDto", "isOptional": false, "defaultValue": null}], "returnValue": {"type": "Volo.Abp.Application.Dtos.PagedResultDto<HolyBless.Albums.Dtos.AlbumDto>", "typeSimple": "Volo.Abp.Application.Dtos.PagedResultDto<HolyBless.Albums.Dtos.AlbumDto>"}}, {"name": "GetAlbumFilesAsync", "parametersOnMethod": [{"name": "albumId", "typeAsString": "System.Int32, System.Private.CoreLib", "type": "System.Int32", "typeSimple": "number", "isOptional": false, "defaultValue": null}], "returnValue": {"type": "System.Collections.Generic.List<HolyBless.Albums.Dtos.AlbumToFileDto>", "typeSimple": "[HolyBless.Albums.Dtos.AlbumToFileDto]"}}]}], "actions": {"GetAsyncById": {"uniqueName": "GetAsyncById", "name": "GetAsync", "httpMethod": "GET", "url": "api/app/read-only-album/{id}", "supportedVersions": [], "parametersOnMethod": [{"name": "id", "typeAsString": "System.Int32, System.Private.CoreLib", "type": "System.Int32", "typeSimple": "number", "isOptional": false, "defaultValue": null}], "parameters": [{"nameOnMethod": "id", "name": "id", "jsonName": null, "type": "System.Int32", "typeSimple": "number", "isOptional": false, "defaultValue": null, "constraintTypes": [], "bindingSourceId": "Path", "descriptorName": ""}], "returnValue": {"type": "HolyBless.Albums.Dtos.AlbumDto", "typeSimple": "HolyBless.Albums.Dtos.AlbumDto"}, "allowAnonymous": null, "implementFrom": "HolyBless.Albums.IReadOnlyAlbumAppService"}, "GetListAsyncByInput": {"uniqueName": "GetListAsyncByInput", "name": "GetListAsync", "httpMethod": "GET", "url": "api/app/read-only-album", "supportedVersions": [], "parametersOnMethod": [{"name": "input", "typeAsString": "HolyBless.Albums.Dtos.AlbumSearchDto, HolyBless.Application.Contracts", "type": "HolyBless.Albums.Dtos.AlbumSearchDto", "typeSimple": "HolyBless.Albums.Dtos.AlbumSearchDto", "isOptional": false, "defaultValue": null}], "parameters": [{"nameOnMethod": "input", "name": "ChannelId", "jsonName": null, "type": "System.Int32?", "typeSimple": "number?", "isOptional": false, "defaultValue": null, "constraintTypes": null, "bindingSourceId": "ModelBinding", "descriptorName": "input"}, {"nameOnMethod": "input", "name": "AlbumType", "jsonName": null, "type": "System.String", "typeSimple": "string", "isOptional": false, "defaultValue": null, "constraintTypes": null, "bindingSourceId": "ModelBinding", "descriptorName": "input"}, {"nameOnMethod": "input", "name": "Sorting", "jsonName": null, "type": "System.String", "typeSimple": "string", "isOptional": false, "defaultValue": null, "constraintTypes": null, "bindingSourceId": "ModelBinding", "descriptorName": "input"}, {"nameOnMethod": "input", "name": "SkipCount", "jsonName": null, "type": "System.Int32", "typeSimple": "number", "isOptional": false, "defaultValue": null, "constraintTypes": null, "bindingSourceId": "ModelBinding", "descriptorName": "input"}, {"nameOnMethod": "input", "name": "MaxResultCount", "jsonName": null, "type": "System.Int32", "typeSimple": "number", "isOptional": false, "defaultValue": null, "constraintTypes": null, "bindingSourceId": "ModelBinding", "descriptorName": "input"}], "returnValue": {"type": "Volo.Abp.Application.Dtos.PagedResultDto<HolyBless.Albums.Dtos.AlbumDto>", "typeSimple": "Volo.Abp.Application.Dtos.PagedResultDto<HolyBless.Albums.Dtos.AlbumDto>"}, "allowAnonymous": null, "implementFrom": "HolyBless.Albums.IReadOnlyAlbumAppService"}, "GetAlbumFilesAsyncByAlbumId": {"uniqueName": "GetAlbumFilesAsyncByAlbumId", "name": "GetAlbumFilesAsync", "httpMethod": "GET", "url": "api/app/read-only-album/album-files/{albumId}", "supportedVersions": [], "parametersOnMethod": [{"name": "albumId", "typeAsString": "System.Int32, System.Private.CoreLib", "type": "System.Int32", "typeSimple": "number", "isOptional": false, "defaultValue": null}], "parameters": [{"nameOnMethod": "albumId", "name": "albumId", "jsonName": null, "type": "System.Int32", "typeSimple": "number", "isOptional": false, "defaultValue": null, "constraintTypes": [], "bindingSourceId": "Path", "descriptorName": ""}], "returnValue": {"type": "System.Collections.Generic.List<HolyBless.Albums.Dtos.AlbumToFileDto>", "typeSimple": "[HolyBless.Albums.Dtos.AlbumToFileDto]"}, "allowAnonymous": null, "implementFrom": "HolyBless.Albums.IReadOnlyAlbumAppService"}}}, "HolyBless.Articles.ReadOnlyArticleAppService": {"controllerName": "ReadOnlyArticle", "controllerGroupName": "ReadOnlyArticle", "isRemoteService": true, "isIntegrationService": false, "apiVersion": null, "type": "HolyBless.Articles.ReadOnlyArticleAppService", "interfaces": [{"type": "HolyBless.Articles.IReadOnlyArticleAppService", "name": "IReadOnlyArticleAppService", "methods": [{"name": "GetAsync", "parametersOnMethod": [{"name": "id", "typeAsString": "System.Int32, System.Private.CoreLib", "type": "System.Int32", "typeSimple": "number", "isOptional": false, "defaultValue": null}], "returnValue": {"type": "HolyBless.Articles.Dtos.ArticleDto", "typeSimple": "HolyBless.Articles.Dtos.ArticleDto"}}, {"name": "GetListAsync", "parametersOnMethod": [{"name": "input", "typeAsString": "Volo.Abp.Application.Dtos.PagedAndSortedResultRequestDto, Volo.Abp.Ddd.Application.Contracts", "type": "Volo.Abp.Application.Dtos.PagedAndSortedResultRequestDto", "typeSimple": "Volo.Abp.Application.Dtos.PagedAndSortedResultRequestDto", "isOptional": false, "defaultValue": null}], "returnValue": {"type": "Volo.Abp.Application.Dtos.PagedResultDto<HolyBless.Articles.Dtos.ArticleDto>", "typeSimple": "Volo.Abp.Application.Dtos.PagedResultDto<HolyBless.Articles.Dtos.ArticleDto>"}}, {"name": "GetTagsAsync", "parametersOnMethod": [{"name": "articleId", "typeAsString": "System.Int32, System.Private.CoreLib", "type": "System.Int32", "typeSimple": "number", "isOptional": false, "defaultValue": null}], "returnValue": {"type": "System.Collections.Generic.List<HolyBless.Tags.Dtos.TagDto>", "typeSimple": "[HolyBless.Tags.Dtos.TagDto]"}}, {"name": "GetTeacherArticleLinksAsync", "parametersOnMethod": [{"name": "studentArticleId", "typeAsString": "System.Int32, System.Private.CoreLib", "type": "System.Int32", "typeSimple": "number", "isOptional": false, "defaultValue": null}, {"name": "skip<PERSON><PERSON>nt", "typeAsString": "System.Int32, System.Private.CoreLib", "type": "System.Int32", "typeSimple": "number", "isOptional": false, "defaultValue": null}, {"name": "maxResultCount", "typeAsString": "System.Int32, System.Private.CoreLib", "type": "System.Int32", "typeSimple": "number", "isOptional": false, "defaultValue": null}, {"name": "sorting", "typeAsString": "System.String, System.Private.CoreLib", "type": "System.String", "typeSimple": "string", "isOptional": true, "defaultValue": null}], "returnValue": {"type": "Volo.Abp.Application.Dtos.PagedResultDto<HolyBless.Articles.Dtos.TeacherArticleLinkDto>", "typeSimple": "Volo.Abp.Application.Dtos.PagedResultDto<HolyBless.Articles.Dtos.TeacherArticleLinkDto>"}}, {"name": "GetArticleAggregateAsync", "parametersOnMethod": [{"name": "articleId", "typeAsString": "System.Int32, System.Private.CoreLib", "type": "System.Int32", "typeSimple": "number", "isOptional": false, "defaultValue": null}], "returnValue": {"type": "HolyBless.Results.ArticleAggregateResult", "typeSimple": "HolyBless.Results.ArticleAggregateResult"}}, {"name": "GetArticleAggregatesByCollectionIdAsync", "parametersOnMethod": [{"name": "collectionId", "typeAsString": "System.Int32, System.Private.CoreLib", "type": "System.Int32", "typeSimple": "number", "isOptional": false, "defaultValue": null}], "returnValue": {"type": "System.Collections.Generic.List<HolyBless.Results.ArticleAggregateResult>", "typeSimple": "[HolyBless.Results.ArticleAggregateResult]"}}]}], "actions": {"GetArticleAggregateAsyncByArticleId": {"uniqueName": "GetArticleAggregateAsyncByArticleId", "name": "GetArticleAggregateAsync", "httpMethod": "GET", "url": "api/app/read-only-article/article-aggregate/{articleId}", "supportedVersions": [], "parametersOnMethod": [{"name": "articleId", "typeAsString": "System.Int32, System.Private.CoreLib", "type": "System.Int32", "typeSimple": "number", "isOptional": false, "defaultValue": null}], "parameters": [{"nameOnMethod": "articleId", "name": "articleId", "jsonName": null, "type": "System.Int32", "typeSimple": "number", "isOptional": false, "defaultValue": null, "constraintTypes": [], "bindingSourceId": "Path", "descriptorName": ""}], "returnValue": {"type": "HolyBless.Results.ArticleAggregateResult", "typeSimple": "HolyBless.Results.ArticleAggregateResult"}, "allowAnonymous": null, "implementFrom": "HolyBless.Articles.IReadOnlyArticleAppService"}, "GetArticleAggregatesByCollectionIdAsyncByCollectionId": {"uniqueName": "GetArticleAggregatesByCollectionIdAsyncByCollectionId", "name": "GetArticleAggregatesByCollectionIdAsync", "httpMethod": "GET", "url": "api/app/read-only-article/article-aggregates-by-collection-id/{collectionId}", "supportedVersions": [], "parametersOnMethod": [{"name": "collectionId", "typeAsString": "System.Int32, System.Private.CoreLib", "type": "System.Int32", "typeSimple": "number", "isOptional": false, "defaultValue": null}], "parameters": [{"nameOnMethod": "collectionId", "name": "collectionId", "jsonName": null, "type": "System.Int32", "typeSimple": "number", "isOptional": false, "defaultValue": null, "constraintTypes": [], "bindingSourceId": "Path", "descriptorName": ""}], "returnValue": {"type": "System.Collections.Generic.List<HolyBless.Results.ArticleAggregateResult>", "typeSimple": "[HolyBless.Results.ArticleAggregateResult]"}, "allowAnonymous": null, "implementFrom": "HolyBless.Articles.IReadOnlyArticleAppService"}}}, "HolyBless.Channels.ReadOnlyChannelAppService": {"controllerName": "ReadOnlyChannel", "controllerGroupName": "ReadOnlyChannel", "isRemoteService": true, "isIntegrationService": false, "apiVersion": null, "type": "HolyBless.Channels.ReadOnlyChannelAppService", "interfaces": [{"type": "HolyBless.Channels.IReadOnlyChannelAppService", "name": "IReadOnlyChannelAppService", "methods": [{"name": "GetAsync", "parametersOnMethod": [{"name": "id", "typeAsString": "System.Int32, System.Private.CoreLib", "type": "System.Int32", "typeSimple": "number", "isOptional": false, "defaultValue": null}], "returnValue": {"type": "HolyBless.Channels.Dtos.ChannelDto", "typeSimple": "HolyBless.Channels.Dtos.ChannelDto"}}, {"name": "GetListAsync", "parametersOnMethod": [{"name": "input", "typeAsString": "HolyBless.Channels.Dtos.ChannelSearchDto, HolyBless.Application.Contracts", "type": "HolyBless.Channels.Dtos.ChannelSearchDto", "typeSimple": "HolyBless.Channels.Dtos.ChannelSearchDto", "isOptional": false, "defaultValue": null}], "returnValue": {"type": "Volo.Abp.Application.Dtos.PagedResultDto<HolyBless.Channels.Dtos.ChannelDto>", "typeSimple": "Volo.Abp.Application.Dtos.PagedResultDto<HolyBless.Channels.Dtos.ChannelDto>"}}, {"name": "GetAllListByLanguageAsync", "parametersOnMethod": [{"name": "languageCode", "typeAsString": "System.String, System.Private.CoreLib", "type": "System.String", "typeSimple": "string", "isOptional": true, "defaultValue": "zh-Hans"}], "returnValue": {"type": "System.Collections.Generic.List<HolyBless.Channels.Dtos.ChannelDto>", "typeSimple": "[HolyBless.Channels.Dtos.ChannelDto]"}}, {"name": "GetChannelTreeAsync", "parametersOnMethod": [{"name": "languageCode", "typeAsString": "System.String, System.Private.CoreLib", "type": "System.String", "typeSimple": "string", "isOptional": false, "defaultValue": null}], "returnValue": {"type": "System.Collections.Generic.List<HolyBless.Channels.Dtos.ChannelTreeDto>", "typeSimple": "[HolyBless.Channels.Dtos.ChannelTreeDto]"}}]}], "actions": {"GetChannelTreeAsyncByLanguageCode": {"uniqueName": "GetChannelTreeAsyncByLanguageCode", "name": "GetChannelTreeAsync", "httpMethod": "GET", "url": "api/app/read-only-channel/channel-tree", "supportedVersions": [], "parametersOnMethod": [{"name": "languageCode", "typeAsString": "System.String, System.Private.CoreLib", "type": "System.String", "typeSimple": "string", "isOptional": false, "defaultValue": null}], "parameters": [{"nameOnMethod": "languageCode", "name": "languageCode", "jsonName": null, "type": "System.String", "typeSimple": "string", "isOptional": false, "defaultValue": null, "constraintTypes": null, "bindingSourceId": "ModelBinding", "descriptorName": ""}], "returnValue": {"type": "System.Collections.Generic.List<HolyBless.Channels.Dtos.ChannelTreeDto>", "typeSimple": "[HolyBless.Channels.Dtos.ChannelTreeDto]"}, "allowAnonymous": null, "implementFrom": "HolyBless.Channels.IReadOnlyChannelAppService"}}}, "HolyBless.Collections.ReadOnlyCollectionAppService": {"controllerName": "ReadOnlyCollection", "controllerGroupName": "ReadOnlyCollection", "isRemoteService": true, "isIntegrationService": false, "apiVersion": null, "type": "HolyBless.Collections.ReadOnlyCollectionAppService", "interfaces": [{"type": "HolyBless.Collections.IReadOnlyCollectionAppService", "name": "IReadOnlyCollectionAppService", "methods": [{"name": "GetAsync", "parametersOnMethod": [{"name": "id", "typeAsString": "System.Int32, System.Private.CoreLib", "type": "System.Int32", "typeSimple": "number", "isOptional": false, "defaultValue": null}], "returnValue": {"type": "HolyBless.Collections.Dtos.CollectionDto", "typeSimple": "HolyBless.Collections.Dtos.CollectionDto"}}, {"name": "GetListAsync", "parametersOnMethod": [{"name": "input", "typeAsString": "HolyBless.Collections.Dtos.CollectionSearchDto, HolyBless.Application.Contracts", "type": "HolyBless.Collections.Dtos.CollectionSearchDto", "typeSimple": "HolyBless.Collections.Dtos.CollectionSearchDto", "isOptional": false, "defaultValue": null}], "returnValue": {"type": "Volo.Abp.Application.Dtos.PagedResultDto<HolyBless.Collections.Dtos.CollectionDto>", "typeSimple": "Volo.Abp.Application.Dtos.PagedResultDto<HolyBless.Collections.Dtos.CollectionDto>"}}, {"name": "GetCollectionArticlesAsync", "parametersOnMethod": [{"name": "input", "typeAsString": "HolyBless.Collections.Dtos.CollectionArticleSearchDto, HolyBless.Application.Contracts", "type": "HolyBless.Collections.Dtos.CollectionArticleSearchDto", "typeSimple": "HolyBless.Collections.Dtos.CollectionArticleSearchDto", "isOptional": false, "defaultValue": null}], "returnValue": {"type": "Volo.Abp.Application.Dtos.PagedResultDto<HolyBless.Collections.Dtos.CollectionToArticleDto>", "typeSimple": "Volo.Abp.Application.Dtos.PagedResultDto<HolyBless.Collections.Dtos.CollectionToArticleDto>"}}, {"name": "GetCollectionFilesAsync", "parametersOnMethod": [{"name": "input", "typeAsString": "HolyBless.Collections.Dtos.CollectionFileSearchDto, HolyBless.Application.Contracts", "type": "HolyBless.Collections.Dtos.CollectionFileSearchDto", "typeSimple": "HolyBless.Collections.Dtos.CollectionFileSearchDto", "isOptional": false, "defaultValue": null}], "returnValue": {"type": "Volo.Abp.Application.Dtos.PagedResultDto<HolyBless.Collections.Dtos.CollectionToFileDto>", "typeSimple": "Volo.Abp.Application.Dtos.PagedResultDto<HolyBless.Collections.Dtos.CollectionToFileDto>"}}, {"name": "GetCollectionSummaryAsync", "parametersOnMethod": [{"name": "collectionId", "typeAsString": "System.Int32, System.Private.CoreLib", "type": "System.Int32", "typeSimple": "number", "isOptional": false, "defaultValue": null}, {"name": "request", "typeAsString": "HolyBless.Results.CollectionSummaryRequest, HolyBless.Domain.Shared", "type": "HolyBless.Results.CollectionSummaryRequest", "typeSimple": "HolyBless.Results.CollectionSummaryRequest", "isOptional": false, "defaultValue": null}], "returnValue": {"type": "HolyBless.Results.CollectionSummaryResult", "typeSimple": "HolyBless.Results.CollectionSummaryResult"}}, {"name": "GetCollectionArticleTitlesAsync", "parametersOnMethod": [{"name": "collectionId", "typeAsString": "System.Int32, System.Private.CoreLib", "type": "System.Int32", "typeSimple": "number", "isOptional": false, "defaultValue": null}], "returnValue": {"type": "System.Collections.Generic.List<HolyBless.Articles.Dtos.ArticleTitleDto>", "typeSimple": "[HolyBless.Articles.Dtos.ArticleTitleDto]"}}, {"name": "GetFirstByChannelIdAsync", "parametersOnMethod": [{"name": "channelId", "typeAsString": "System.Int32, System.Private.CoreLib", "type": "System.Int32", "typeSimple": "number", "isOptional": false, "defaultValue": null}], "returnValue": {"type": "HolyBless.Collections.Dtos.CollectionDto", "typeSimple": "HolyBless.Collections.Dtos.CollectionDto"}}, {"name": "GetLanguageMatchingCollectionAsync", "parametersOnMethod": [{"name": "contentCode", "typeAsString": "System.String, System.Private.CoreLib", "type": "System.String", "typeSimple": "string", "isOptional": false, "defaultValue": null}, {"name": "languageCode", "typeAsString": "System.String, System.Private.CoreLib", "type": "System.String", "typeSimple": "string", "isOptional": false, "defaultValue": null}], "returnValue": {"type": "HolyBless.Collections.Dtos.CollectionDto", "typeSimple": "HolyBless.Collections.Dtos.CollectionDto"}}, {"name": "GetCollectionTreeAsync", "parametersOnMethod": [{"name": "collectionId", "typeAsString": "System.Int32, System.Private.CoreLib", "type": "System.Int32", "typeSimple": "number", "isOptional": false, "defaultValue": null}], "returnValue": {"type": "System.Collections.Generic.List<HolyBless.Collections.Dtos.CollectionTreeDto>", "typeSimple": "[HolyBless.Collections.Dtos.CollectionTreeDto]"}}, {"name": "GetCollectionTreeAndArticleTitlesAsync", "parametersOnMethod": [{"name": "collectionId", "typeAsString": "System.Int32, System.Private.CoreLib", "type": "System.Int32", "typeSimple": "number", "isOptional": false, "defaultValue": null}], "returnValue": {"type": "System.Collections.Generic.List<HolyBless.Collections.Dtos.CollectionArticleTreeDto>", "typeSimple": "[HolyBless.Collections.Dtos.CollectionArticleTreeDto]"}}]}], "actions": {"GetAsyncById": {"uniqueName": "GetAsyncById", "name": "GetAsync", "httpMethod": "GET", "url": "api/app/read-only-collection/{id}", "supportedVersions": [], "parametersOnMethod": [{"name": "id", "typeAsString": "System.Int32, System.Private.CoreLib", "type": "System.Int32", "typeSimple": "number", "isOptional": false, "defaultValue": null}], "parameters": [{"nameOnMethod": "id", "name": "id", "jsonName": null, "type": "System.Int32", "typeSimple": "number", "isOptional": false, "defaultValue": null, "constraintTypes": [], "bindingSourceId": "Path", "descriptorName": ""}], "returnValue": {"type": "HolyBless.Collections.Dtos.CollectionDto", "typeSimple": "HolyBless.Collections.Dtos.CollectionDto"}, "allowAnonymous": null, "implementFrom": "HolyBless.Collections.IReadOnlyCollectionAppService"}, "GetCollectionSummaryAsyncByCollectionIdAndRequest": {"uniqueName": "GetCollectionSummaryAsyncByCollectionIdAndRequest", "name": "GetCollectionSummaryAsync", "httpMethod": "GET", "url": "api/app/read-only-collection/collection-summary/{collectionId}", "supportedVersions": [], "parametersOnMethod": [{"name": "collectionId", "typeAsString": "System.Int32, System.Private.CoreLib", "type": "System.Int32", "typeSimple": "number", "isOptional": false, "defaultValue": null}, {"name": "request", "typeAsString": "HolyBless.Results.CollectionSummaryRequest, HolyBless.Domain.Shared", "type": "HolyBless.Results.CollectionSummaryRequest", "typeSimple": "HolyBless.Results.CollectionSummaryRequest", "isOptional": false, "defaultValue": null}], "parameters": [{"nameOnMethod": "collectionId", "name": "collectionId", "jsonName": null, "type": "System.Int32", "typeSimple": "number", "isOptional": false, "defaultValue": null, "constraintTypes": [], "bindingSourceId": "Path", "descriptorName": ""}, {"nameOnMethod": "request", "name": "<PERSON><PERSON>", "jsonName": null, "type": "System.Int32", "typeSimple": "number", "isOptional": false, "defaultValue": null, "constraintTypes": null, "bindingSourceId": "ModelBinding", "descriptorName": "request"}, {"nameOnMethod": "request", "name": "MaxResultCount", "jsonName": null, "type": "System.Int32", "typeSimple": "number", "isOptional": false, "defaultValue": null, "constraintTypes": null, "bindingSourceId": "ModelBinding", "descriptorName": "request"}, {"nameOnMethod": "request", "name": "Sorting", "jsonName": null, "type": "System.String", "typeSimple": "string", "isOptional": false, "defaultValue": null, "constraintTypes": null, "bindingSourceId": "ModelBinding", "descriptorName": "request"}, {"nameOnMethod": "request", "name": "Year", "jsonName": null, "type": "System.Int32?", "typeSimple": "number?", "isOptional": false, "defaultValue": null, "constraintTypes": null, "bindingSourceId": "ModelBinding", "descriptorName": "request"}, {"nameOnMethod": "request", "name": "Month", "jsonName": null, "type": "System.Int32?", "typeSimple": "number?", "isOptional": false, "defaultValue": null, "constraintTypes": null, "bindingSourceId": "ModelBinding", "descriptorName": "request"}], "returnValue": {"type": "HolyBless.Results.CollectionSummaryResult", "typeSimple": "HolyBless.Results.CollectionSummaryResult"}, "allowAnonymous": null, "implementFrom": "HolyBless.Collections.IReadOnlyCollectionAppService"}, "GetCollectionArticleTitlesAsyncByCollectionId": {"uniqueName": "GetCollectionArticleTitlesAsyncByCollectionId", "name": "GetCollectionArticleTitlesAsync", "httpMethod": "GET", "url": "api/app/read-only-collection/collection-article-titles/{collectionId}", "supportedVersions": [], "parametersOnMethod": [{"name": "collectionId", "typeAsString": "System.Int32, System.Private.CoreLib", "type": "System.Int32", "typeSimple": "number", "isOptional": false, "defaultValue": null}], "parameters": [{"nameOnMethod": "collectionId", "name": "collectionId", "jsonName": null, "type": "System.Int32", "typeSimple": "number", "isOptional": false, "defaultValue": null, "constraintTypes": [], "bindingSourceId": "Path", "descriptorName": ""}], "returnValue": {"type": "System.Collections.Generic.List<HolyBless.Articles.Dtos.ArticleTitleDto>", "typeSimple": "[HolyBless.Articles.Dtos.ArticleTitleDto]"}, "allowAnonymous": null, "implementFrom": "HolyBless.Collections.IReadOnlyCollectionAppService"}, "GetFirstByChannelIdAsyncByChannelId": {"uniqueName": "GetFirstByChannelIdAsyncByChannelId", "name": "GetFirstByChannelIdAsync", "httpMethod": "GET", "url": "api/app/read-only-collection/first-by-channel-id/{channelId}", "supportedVersions": [], "parametersOnMethod": [{"name": "channelId", "typeAsString": "System.Int32, System.Private.CoreLib", "type": "System.Int32", "typeSimple": "number", "isOptional": false, "defaultValue": null}], "parameters": [{"nameOnMethod": "channelId", "name": "channelId", "jsonName": null, "type": "System.Int32", "typeSimple": "number", "isOptional": false, "defaultValue": null, "constraintTypes": [], "bindingSourceId": "Path", "descriptorName": ""}], "returnValue": {"type": "HolyBless.Collections.Dtos.CollectionDto", "typeSimple": "HolyBless.Collections.Dtos.CollectionDto"}, "allowAnonymous": null, "implementFrom": "HolyBless.Collections.IReadOnlyCollectionAppService"}, "GetLanguageMatchingCollectionAsyncByContentCodeAndLanguageCode": {"uniqueName": "GetLanguageMatchingCollectionAsyncByContentCodeAndLanguageCode", "name": "GetLanguageMatchingCollectionAsync", "httpMethod": "GET", "url": "api/app/read-only-collection/language-matching-collection", "supportedVersions": [], "parametersOnMethod": [{"name": "contentCode", "typeAsString": "System.String, System.Private.CoreLib", "type": "System.String", "typeSimple": "string", "isOptional": false, "defaultValue": null}, {"name": "languageCode", "typeAsString": "System.String, System.Private.CoreLib", "type": "System.String", "typeSimple": "string", "isOptional": false, "defaultValue": null}], "parameters": [{"nameOnMethod": "contentCode", "name": "contentCode", "jsonName": null, "type": "System.String", "typeSimple": "string", "isOptional": false, "defaultValue": null, "constraintTypes": null, "bindingSourceId": "ModelBinding", "descriptorName": ""}, {"nameOnMethod": "languageCode", "name": "languageCode", "jsonName": null, "type": "System.String", "typeSimple": "string", "isOptional": false, "defaultValue": null, "constraintTypes": null, "bindingSourceId": "ModelBinding", "descriptorName": ""}], "returnValue": {"type": "HolyBless.Collections.Dtos.CollectionDto", "typeSimple": "HolyBless.Collections.Dtos.CollectionDto"}, "allowAnonymous": null, "implementFrom": "HolyBless.Collections.IReadOnlyCollectionAppService"}, "GetCollectionTreeAsyncByCollectionId": {"uniqueName": "GetCollectionTreeAsyncByCollectionId", "name": "GetCollectionTreeAsync", "httpMethod": "GET", "url": "api/app/read-only-collection/collection-tree/{collectionId}", "supportedVersions": [], "parametersOnMethod": [{"name": "collectionId", "typeAsString": "System.Int32, System.Private.CoreLib", "type": "System.Int32", "typeSimple": "number", "isOptional": false, "defaultValue": null}], "parameters": [{"nameOnMethod": "collectionId", "name": "collectionId", "jsonName": null, "type": "System.Int32", "typeSimple": "number", "isOptional": false, "defaultValue": null, "constraintTypes": [], "bindingSourceId": "Path", "descriptorName": ""}], "returnValue": {"type": "System.Collections.Generic.List<HolyBless.Collections.Dtos.CollectionTreeDto>", "typeSimple": "[HolyBless.Collections.Dtos.CollectionTreeDto]"}, "allowAnonymous": null, "implementFrom": "HolyBless.Collections.IReadOnlyCollectionAppService"}, "GetCollectionTreeAndArticleTitlesAsyncByCollectionId": {"uniqueName": "GetCollectionTreeAndArticleTitlesAsyncByCollectionId", "name": "GetCollectionTreeAndArticleTitlesAsync", "httpMethod": "GET", "url": "api/app/read-only-collection/collection-tree-and-article-titles/{collectionId}", "supportedVersions": [], "parametersOnMethod": [{"name": "collectionId", "typeAsString": "System.Int32, System.Private.CoreLib", "type": "System.Int32", "typeSimple": "number", "isOptional": false, "defaultValue": null}], "parameters": [{"nameOnMethod": "collectionId", "name": "collectionId", "jsonName": null, "type": "System.Int32", "typeSimple": "number", "isOptional": false, "defaultValue": null, "constraintTypes": [], "bindingSourceId": "Path", "descriptorName": ""}], "returnValue": {"type": "System.Collections.Generic.List<HolyBless.Collections.Dtos.CollectionArticleTreeDto>", "typeSimple": "[HolyBless.Collections.Dtos.CollectionArticleTreeDto]"}, "allowAnonymous": null, "implementFrom": "HolyBless.Collections.IReadOnlyCollectionAppService"}}}, "HolyBless.HttpApi.Host.Controllers.Common.PingController": {"controllerName": "<PERSON>", "controllerGroupName": null, "isRemoteService": false, "isIntegrationService": false, "apiVersion": null, "type": "HolyBless.HttpApi.Host.Controllers.Common.PingController", "interfaces": [], "actions": {"Ping": {"uniqueName": "<PERSON>", "name": "<PERSON>", "httpMethod": "HEAD", "url": "api/common/ping", "supportedVersions": [], "parametersOnMethod": [], "parameters": [], "returnValue": {"type": "Microsoft.AspNetCore.Mvc.IActionResult", "typeSimple": "Microsoft.AspNetCore.Mvc.IActionResult"}, "allowAnonymous": true, "implementFrom": "HolyBless.HttpApi.Host.Controllers.Common.PingController"}}}, "HolyBless.Lookups.ReadOnlyCountryAppService": {"controllerName": "ReadOnlyCountry", "controllerGroupName": "ReadOnlyCountry", "isRemoteService": true, "isIntegrationService": false, "apiVersion": null, "type": "HolyBless.Lookups.ReadOnlyCountryAppService", "interfaces": [{"type": "HolyBless.Lookups.IReadOnlyCountryAppService", "name": "IReadOnlyCountryAppService", "methods": [{"name": "GetAsync", "parametersOnMethod": [{"name": "id", "typeAsString": "System.Int32, System.Private.CoreLib", "type": "System.Int32", "typeSimple": "number", "isOptional": false, "defaultValue": null}], "returnValue": {"type": "HolyBless.Lookups.Dtos.CountryDto", "typeSimple": "HolyBless.Lookups.Dtos.CountryDto"}}, {"name": "GetListAsync", "parametersOnMethod": [{"name": "input", "typeAsString": "Volo.Abp.Application.Dtos.PagedAndSortedResultRequestDto, Volo.Abp.Ddd.Application.Contracts", "type": "Volo.Abp.Application.Dtos.PagedAndSortedResultRequestDto", "typeSimple": "Volo.Abp.Application.Dtos.PagedAndSortedResultRequestDto", "isOptional": false, "defaultValue": null}], "returnValue": {"type": "Volo.Abp.Application.Dtos.PagedResultDto<HolyBless.Lookups.Dtos.CountryDto>", "typeSimple": "Volo.Abp.Application.Dtos.PagedResultDto<HolyBless.Lookups.Dtos.CountryDto>"}}]}], "actions": {"GetAsyncById": {"uniqueName": "GetAsyncById", "name": "GetAsync", "httpMethod": "GET", "url": "api/app/read-only-country/{id}", "supportedVersions": [], "parametersOnMethod": [{"name": "id", "typeAsString": "System.Int32, System.Private.CoreLib", "type": "System.Int32", "typeSimple": "number", "isOptional": false, "defaultValue": null}], "parameters": [{"nameOnMethod": "id", "name": "id", "jsonName": null, "type": "System.Int32", "typeSimple": "number", "isOptional": false, "defaultValue": null, "constraintTypes": [], "bindingSourceId": "Path", "descriptorName": ""}], "returnValue": {"type": "HolyBless.Lookups.Dtos.CountryDto", "typeSimple": "HolyBless.Lookups.Dtos.CountryDto"}, "allowAnonymous": null, "implementFrom": "HolyBless.Lookups.IReadOnlyCountryAppService"}, "GetListAsyncByInput": {"uniqueName": "GetListAsyncByInput", "name": "GetListAsync", "httpMethod": "GET", "url": "api/app/read-only-country", "supportedVersions": [], "parametersOnMethod": [{"name": "input", "typeAsString": "Volo.Abp.Application.Dtos.PagedAndSortedResultRequestDto, Volo.Abp.Ddd.Application.Contracts", "type": "Volo.Abp.Application.Dtos.PagedAndSortedResultRequestDto", "typeSimple": "Volo.Abp.Application.Dtos.PagedAndSortedResultRequestDto", "isOptional": false, "defaultValue": null}], "parameters": [{"nameOnMethod": "input", "name": "Sorting", "jsonName": null, "type": "System.String", "typeSimple": "string", "isOptional": false, "defaultValue": null, "constraintTypes": null, "bindingSourceId": "ModelBinding", "descriptorName": "input"}, {"nameOnMethod": "input", "name": "SkipCount", "jsonName": null, "type": "System.Int32", "typeSimple": "number", "isOptional": false, "defaultValue": null, "constraintTypes": null, "bindingSourceId": "ModelBinding", "descriptorName": "input"}, {"nameOnMethod": "input", "name": "MaxResultCount", "jsonName": null, "type": "System.Int32", "typeSimple": "number", "isOptional": false, "defaultValue": null, "constraintTypes": null, "bindingSourceId": "ModelBinding", "descriptorName": "input"}], "returnValue": {"type": "Volo.Abp.Application.Dtos.PagedResultDto<HolyBless.Lookups.Dtos.CountryDto>", "typeSimple": "Volo.Abp.Application.Dtos.PagedResultDto<HolyBless.Lookups.Dtos.CountryDto>"}, "allowAnonymous": null, "implementFrom": "HolyBless.Lookups.IReadOnlyCountryAppService"}}}, "HolyBless.Tags.ReadOnlyTagAppService": {"controllerName": "ReadOnlyTag", "controllerGroupName": "ReadOnlyTag", "isRemoteService": true, "isIntegrationService": false, "apiVersion": null, "type": "HolyBless.Tags.ReadOnlyTagAppService", "interfaces": [{"type": "HolyBless.Tags.IReadOnlyTagAppService", "name": "IReadOnlyTagAppService", "methods": [{"name": "GetAsync", "parametersOnMethod": [{"name": "id", "typeAsString": "System.Int32, System.Private.CoreLib", "type": "System.Int32", "typeSimple": "number", "isOptional": false, "defaultValue": null}], "returnValue": {"type": "HolyBless.Tags.Dtos.TagDto", "typeSimple": "HolyBless.Tags.Dtos.TagDto"}}, {"name": "GetListAsync", "parametersOnMethod": [{"name": "input", "typeAsString": "HolyBless.Tags.Dtos.TagSearchDto, HolyBless.Application.Contracts", "type": "HolyBless.Tags.Dtos.TagSearchDto", "typeSimple": "HolyBless.Tags.Dtos.TagSearchDto", "isOptional": false, "defaultValue": null}], "returnValue": {"type": "Volo.Abp.Application.Dtos.PagedResultDto<HolyBless.Tags.Dtos.TagDto>", "typeSimple": "Volo.Abp.Application.Dtos.PagedResultDto<HolyBless.Tags.Dtos.TagDto>"}}]}], "actions": {"GetAsyncById": {"uniqueName": "GetAsyncById", "name": "GetAsync", "httpMethod": "GET", "url": "api/app/read-only-tag/{id}", "supportedVersions": [], "parametersOnMethod": [{"name": "id", "typeAsString": "System.Int32, System.Private.CoreLib", "type": "System.Int32", "typeSimple": "number", "isOptional": false, "defaultValue": null}], "parameters": [{"nameOnMethod": "id", "name": "id", "jsonName": null, "type": "System.Int32", "typeSimple": "number", "isOptional": false, "defaultValue": null, "constraintTypes": [], "bindingSourceId": "Path", "descriptorName": ""}], "returnValue": {"type": "HolyBless.Tags.Dtos.TagDto", "typeSimple": "HolyBless.Tags.Dtos.TagDto"}, "allowAnonymous": null, "implementFrom": "HolyBless.Tags.IReadOnlyTagAppService"}, "GetListAsyncByInput": {"uniqueName": "GetListAsyncByInput", "name": "GetListAsync", "httpMethod": "GET", "url": "api/app/read-only-tag", "supportedVersions": [], "parametersOnMethod": [{"name": "input", "typeAsString": "HolyBless.Tags.Dtos.TagSearchDto, HolyBless.Application.Contracts", "type": "HolyBless.Tags.Dtos.TagSearchDto", "typeSimple": "HolyBless.Tags.Dtos.TagSearchDto", "isOptional": false, "defaultValue": null}], "parameters": [{"nameOnMethod": "input", "name": "TagName", "jsonName": null, "type": "System.String", "typeSimple": "string", "isOptional": false, "defaultValue": null, "constraintTypes": null, "bindingSourceId": "ModelBinding", "descriptorName": "input"}, {"nameOnMethod": "input", "name": "ContentCode", "jsonName": null, "type": "System.String", "typeSimple": "string", "isOptional": false, "defaultValue": null, "constraintTypes": null, "bindingSourceId": "ModelBinding", "descriptorName": "input"}, {"nameOnMethod": "input", "name": "Sorting", "jsonName": null, "type": "System.String", "typeSimple": "string", "isOptional": false, "defaultValue": null, "constraintTypes": null, "bindingSourceId": "ModelBinding", "descriptorName": "input"}, {"nameOnMethod": "input", "name": "SkipCount", "jsonName": null, "type": "System.Int32", "typeSimple": "number", "isOptional": false, "defaultValue": null, "constraintTypes": null, "bindingSourceId": "ModelBinding", "descriptorName": "input"}, {"nameOnMethod": "input", "name": "MaxResultCount", "jsonName": null, "type": "System.Int32", "typeSimple": "number", "isOptional": false, "defaultValue": null, "constraintTypes": null, "bindingSourceId": "ModelBinding", "descriptorName": "input"}], "returnValue": {"type": "Volo.Abp.Application.Dtos.PagedResultDto<HolyBless.Tags.Dtos.TagDto>", "typeSimple": "Volo.Abp.Application.Dtos.PagedResultDto<HolyBless.Tags.Dtos.TagDto>"}, "allowAnonymous": null, "implementFrom": "HolyBless.Tags.IReadOnlyTagAppService"}}}, "HolyBless.VirtualFolders.ReadOnlyVirtualFolderAppService": {"controllerName": "ReadOnlyVirtualFolder", "controllerGroupName": "ReadOnlyVirtualFolder", "isRemoteService": true, "isIntegrationService": false, "apiVersion": null, "type": "HolyBless.VirtualFolders.ReadOnlyVirtualFolderAppService", "interfaces": [{"type": "HolyBless.VirtualFolders.IReadOnlyVirtualFolderAppService", "name": "IReadOnlyVirtualFolderAppService", "methods": [{"name": "GetFolderTreeJson", "parametersOnMethod": [{"name": "rootFolderId", "typeAsString": "System.Int32, System.Private.CoreLib", "type": "System.Int32", "typeSimple": "number", "isOptional": false, "defaultValue": null}], "returnValue": {"type": "System.String", "typeSimple": "string"}}, {"name": "GetFolderTreeWithFilesAsync", "parametersOnMethod": [{"name": "rootFolderId", "typeAsString": "System.Int32, System.Private.CoreLib", "type": "System.Int32", "typeSimple": "number", "isOptional": false, "defaultValue": null}], "returnValue": {"type": "HolyBless.VirtualFolders.VirtualFolderTreeDto", "typeSimple": "HolyBless.VirtualFolders.VirtualFolderTreeDto"}}]}], "actions": {"GetFolderTreeJsonByRootFolderId": {"uniqueName": "GetFolderTreeJsonByRootFolderId", "name": "GetFolderTreeJson", "httpMethod": "GET", "url": "api/app/read-only-virtual-folder/folder-tree-json/{rootFolderId}", "supportedVersions": [], "parametersOnMethod": [{"name": "rootFolderId", "typeAsString": "System.Int32, System.Private.CoreLib", "type": "System.Int32", "typeSimple": "number", "isOptional": false, "defaultValue": null}], "parameters": [{"nameOnMethod": "rootFolderId", "name": "rootFolderId", "jsonName": null, "type": "System.Int32", "typeSimple": "number", "isOptional": false, "defaultValue": null, "constraintTypes": [], "bindingSourceId": "Path", "descriptorName": ""}], "returnValue": {"type": "System.String", "typeSimple": "string"}, "allowAnonymous": null, "implementFrom": "HolyBless.VirtualFolders.IReadOnlyVirtualFolderAppService"}, "GetFolderTreeWithFilesAsyncByRootFolderId": {"uniqueName": "GetFolderTreeWithFilesAsyncByRootFolderId", "name": "GetFolderTreeWithFilesAsync", "httpMethod": "GET", "url": "api/app/read-only-virtual-folder/folder-tree-with-files/{rootFolderId}", "supportedVersions": [], "parametersOnMethod": [{"name": "rootFolderId", "typeAsString": "System.Int32, System.Private.CoreLib", "type": "System.Int32", "typeSimple": "number", "isOptional": false, "defaultValue": null}], "parameters": [{"nameOnMethod": "rootFolderId", "name": "rootFolderId", "jsonName": null, "type": "System.Int32", "typeSimple": "number", "isOptional": false, "defaultValue": null, "constraintTypes": [], "bindingSourceId": "Path", "descriptorName": ""}], "returnValue": {"type": "HolyBless.VirtualFolders.VirtualFolderTreeDto", "typeSimple": "HolyBless.VirtualFolders.VirtualFolderTreeDto"}, "allowAnonymous": null, "implementFrom": "HolyBless.VirtualFolders.IReadOnlyVirtualFolderAppService"}}}}}, "featureManagement": {"rootPath": "featureManagement", "remoteServiceName": "AbpFeatureManagement", "controllers": {"Volo.Abp.FeatureManagement.FeaturesController": {"controllerName": "Features", "controllerGroupName": "Features", "isRemoteService": true, "isIntegrationService": false, "apiVersion": null, "type": "Volo.Abp.FeatureManagement.FeaturesController", "interfaces": [{"type": "Volo.Abp.FeatureManagement.IFeatureAppService", "name": "IFeatureAppService", "methods": [{"name": "GetAsync", "parametersOnMethod": [{"name": "providerName", "typeAsString": "System.String, System.Private.CoreLib", "type": "System.String", "typeSimple": "string", "isOptional": false, "defaultValue": null}, {"name": "providerKey", "typeAsString": "System.String, System.Private.CoreLib", "type": "System.String", "typeSimple": "string", "isOptional": false, "defaultValue": null}], "returnValue": {"type": "Volo.Abp.FeatureManagement.GetFeatureListResultDto", "typeSimple": "Volo.Abp.FeatureManagement.GetFeatureListResultDto"}}, {"name": "UpdateAsync", "parametersOnMethod": [{"name": "providerName", "typeAsString": "System.String, System.Private.CoreLib", "type": "System.String", "typeSimple": "string", "isOptional": false, "defaultValue": null}, {"name": "providerKey", "typeAsString": "System.String, System.Private.CoreLib", "type": "System.String", "typeSimple": "string", "isOptional": false, "defaultValue": null}, {"name": "input", "typeAsString": "Volo.Abp.FeatureManagement.UpdateFeaturesDto, Volo.Abp.FeatureManagement.Application.Contracts", "type": "Volo.Abp.FeatureManagement.UpdateFeaturesDto", "typeSimple": "Volo.Abp.FeatureManagement.UpdateFeaturesDto", "isOptional": false, "defaultValue": null}], "returnValue": {"type": "System.Void", "typeSimple": "System.Void"}}, {"name": "DeleteAsync", "parametersOnMethod": [{"name": "providerName", "typeAsString": "System.String, System.Private.CoreLib", "type": "System.String", "typeSimple": "string", "isOptional": false, "defaultValue": null}, {"name": "providerKey", "typeAsString": "System.String, System.Private.CoreLib", "type": "System.String", "typeSimple": "string", "isOptional": false, "defaultValue": null}], "returnValue": {"type": "System.Void", "typeSimple": "System.Void"}}]}], "actions": {"GetAsyncByProviderNameAndProviderKey": {"uniqueName": "GetAsyncByProviderNameAndProviderKey", "name": "GetAsync", "httpMethod": "GET", "url": "api/feature-management/features", "supportedVersions": [], "parametersOnMethod": [{"name": "providerName", "typeAsString": "System.String, System.Private.CoreLib", "type": "System.String", "typeSimple": "string", "isOptional": false, "defaultValue": null}, {"name": "providerKey", "typeAsString": "System.String, System.Private.CoreLib", "type": "System.String", "typeSimple": "string", "isOptional": false, "defaultValue": null}], "parameters": [{"nameOnMethod": "providerName", "name": "providerName", "jsonName": null, "type": "System.String", "typeSimple": "string", "isOptional": false, "defaultValue": null, "constraintTypes": null, "bindingSourceId": "ModelBinding", "descriptorName": ""}, {"nameOnMethod": "providerKey", "name": "providerKey", "jsonName": null, "type": "System.String", "typeSimple": "string", "isOptional": false, "defaultValue": null, "constraintTypes": null, "bindingSourceId": "ModelBinding", "descriptorName": ""}], "returnValue": {"type": "Volo.Abp.FeatureManagement.GetFeatureListResultDto", "typeSimple": "Volo.Abp.FeatureManagement.GetFeatureListResultDto"}, "allowAnonymous": null, "implementFrom": "Volo.Abp.FeatureManagement.IFeatureAppService"}, "UpdateAsyncByProviderNameAndProviderKeyAndInput": {"uniqueName": "UpdateAsyncByProviderNameAndProviderKeyAndInput", "name": "UpdateAsync", "httpMethod": "PUT", "url": "api/feature-management/features", "supportedVersions": [], "parametersOnMethod": [{"name": "providerName", "typeAsString": "System.String, System.Private.CoreLib", "type": "System.String", "typeSimple": "string", "isOptional": false, "defaultValue": null}, {"name": "providerKey", "typeAsString": "System.String, System.Private.CoreLib", "type": "System.String", "typeSimple": "string", "isOptional": false, "defaultValue": null}, {"name": "input", "typeAsString": "Volo.Abp.FeatureManagement.UpdateFeaturesDto, Volo.Abp.FeatureManagement.Application.Contracts", "type": "Volo.Abp.FeatureManagement.UpdateFeaturesDto", "typeSimple": "Volo.Abp.FeatureManagement.UpdateFeaturesDto", "isOptional": false, "defaultValue": null}], "parameters": [{"nameOnMethod": "providerName", "name": "providerName", "jsonName": null, "type": "System.String", "typeSimple": "string", "isOptional": false, "defaultValue": null, "constraintTypes": null, "bindingSourceId": "ModelBinding", "descriptorName": ""}, {"nameOnMethod": "providerKey", "name": "providerKey", "jsonName": null, "type": "System.String", "typeSimple": "string", "isOptional": false, "defaultValue": null, "constraintTypes": null, "bindingSourceId": "ModelBinding", "descriptorName": ""}, {"nameOnMethod": "input", "name": "input", "jsonName": null, "type": "Volo.Abp.FeatureManagement.UpdateFeaturesDto", "typeSimple": "Volo.Abp.FeatureManagement.UpdateFeaturesDto", "isOptional": false, "defaultValue": null, "constraintTypes": null, "bindingSourceId": "Body", "descriptorName": ""}], "returnValue": {"type": "System.Void", "typeSimple": "System.Void"}, "allowAnonymous": null, "implementFrom": "Volo.Abp.FeatureManagement.IFeatureAppService"}, "DeleteAsyncByProviderNameAndProviderKey": {"uniqueName": "DeleteAsyncByProviderNameAndProviderKey", "name": "DeleteAsync", "httpMethod": "DELETE", "url": "api/feature-management/features", "supportedVersions": [], "parametersOnMethod": [{"name": "providerName", "typeAsString": "System.String, System.Private.CoreLib", "type": "System.String", "typeSimple": "string", "isOptional": false, "defaultValue": null}, {"name": "providerKey", "typeAsString": "System.String, System.Private.CoreLib", "type": "System.String", "typeSimple": "string", "isOptional": false, "defaultValue": null}], "parameters": [{"nameOnMethod": "providerName", "name": "providerName", "jsonName": null, "type": "System.String", "typeSimple": "string", "isOptional": false, "defaultValue": null, "constraintTypes": null, "bindingSourceId": "ModelBinding", "descriptorName": ""}, {"nameOnMethod": "providerKey", "name": "providerKey", "jsonName": null, "type": "System.String", "typeSimple": "string", "isOptional": false, "defaultValue": null, "constraintTypes": null, "bindingSourceId": "ModelBinding", "descriptorName": ""}], "returnValue": {"type": "System.Void", "typeSimple": "System.Void"}, "allowAnonymous": null, "implementFrom": "Volo.Abp.FeatureManagement.IFeatureAppService"}}}}}}, "types": {"HolyBless.Albums.Dtos.AlbumDto": {"baseType": "Volo.Abp.Application.Dtos.AuditedEntityDto<System.Int32>", "isEnum": false, "enumNames": null, "enumValues": null, "genericArguments": null, "properties": [{"name": "ChannelId", "jsonName": null, "type": "System.Int32?", "typeSimple": "number?", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "ChannelName", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "ChannelContentCode", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "ThumbnailFileId", "jsonName": null, "type": "System.Int32?", "typeSimple": "number?", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "ThumbnailUrl", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "ContentCode", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "LanguageCode", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "Title", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "Description", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "Views", "jsonName": null, "type": "System.Int32", "typeSimple": "number", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "<PERSON>s", "jsonName": null, "type": "System.Int32", "typeSimple": "number", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "Weight", "jsonName": null, "type": "System.Int32", "typeSimple": "number", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "AlbumType", "jsonName": null, "type": "HolyBless.Enums.AlbumType", "typeSimple": "enum", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}]}, "HolyBless.Albums.Dtos.AlbumSearchDto": {"baseType": "Volo.Abp.Application.Dtos.PagedAndSortedResultRequestDto", "isEnum": false, "enumNames": null, "enumValues": null, "genericArguments": null, "properties": [{"name": "ChannelId", "jsonName": null, "type": "System.Int32?", "typeSimple": "number?", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "AlbumType", "jsonName": null, "type": "HolyBless.Enums.AlbumType?", "typeSimple": "enum?", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}]}, "HolyBless.Albums.Dtos.AlbumToFileDto": {"baseType": "Volo.Abp.Application.Dtos.EntityDto", "isEnum": false, "enumNames": null, "enumValues": null, "genericArguments": null, "properties": [{"name": "AlbumId", "jsonName": null, "type": "System.Int32", "typeSimple": "number", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "FileId", "jsonName": null, "type": "System.Int32", "typeSimple": "number", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "Title", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "Weight", "jsonName": null, "type": "System.Int32", "typeSimple": "number", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "FileName", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "FileUrl", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "AlbumTitle", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}]}, "HolyBless.Articles.Dtos.ArticleTitleDto": {"baseType": "Volo.Abp.Application.Dtos.EntityDto<System.Int32>", "isEnum": false, "enumNames": null, "enumValues": null, "genericArguments": null, "properties": [{"name": "Title", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}]}, "HolyBless.Buckets.Dtos.BucketFileDto": {"baseType": "Volo.Abp.Application.Dtos.EntityDto<System.Int32>", "isEnum": false, "enumNames": null, "enumValues": null, "genericArguments": null, "properties": [{"name": "FileName", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "Title", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "RelativePathInBucket", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "LanguageCode", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "SpokenLangCode", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "MediaType", "jsonName": null, "type": "HolyBless.Enums.MediaType", "typeSimple": "enum", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "ContentCategory", "jsonName": null, "type": "HolyBless.Enums.ContentCategory", "typeSimple": "enum", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "DeliveryDate", "jsonName": null, "type": "System.DateTime?", "typeSimple": "string?", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "Size", "jsonName": null, "type": "System.Int64?", "typeSimple": "number?", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "Views", "jsonName": null, "type": "System.Int32", "typeSimple": "number", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "YoutubeId", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "Environment", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "Exists", "jsonName": null, "type": "System.Boolean", "typeSimple": "boolean", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "ComputeUrl", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}]}, "HolyBless.Channels.Dtos.ChannelTreeDto": {"baseType": null, "isEnum": false, "enumNames": null, "enumValues": null, "genericArguments": null, "properties": [{"name": "Id", "jsonName": null, "type": "System.Int32", "typeSimple": "number", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "Name", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "ContentCode", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "IsRoot", "jsonName": null, "type": "System.Boolean", "typeSimple": "boolean", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "ChannelSource", "jsonName": null, "type": "HolyBless.Enums.ChannelSource?", "typeSimple": "enum?", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "Children", "jsonName": null, "type": "[HolyBless.Channels.Dtos.ChannelTreeDto]", "typeSimple": "[HolyBless.Channels.Dtos.ChannelTreeDto]", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}]}, "HolyBless.Collections.Dtos.CollectionArticleTreeDto": {"baseType": null, "isEnum": false, "enumNames": null, "enumValues": null, "genericArguments": null, "properties": [{"name": "Id", "jsonName": null, "type": "System.Int32", "typeSimple": "number", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "Name", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "ContentCode", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "ParentCollectionId", "jsonName": null, "type": "System.Int32?", "typeSimple": "number?", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "Description", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "Status", "jsonName": null, "type": "HolyBless.Enums.PublishStatus", "typeSimple": "enum", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "IsRoot", "jsonName": null, "type": "System.Boolean", "typeSimple": "boolean", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "Children", "jsonName": null, "type": "[HolyBless.Collections.Dtos.CollectionArticleTreeDto]", "typeSimple": "[HolyBless.Collections.Dtos.CollectionArticleTreeDto]", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "Articles", "jsonName": null, "type": "[HolyBless.Articles.Dtos.ArticleTitleDto]", "typeSimple": "[HolyBless.Articles.Dtos.ArticleTitleDto]", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}]}, "HolyBless.Collections.Dtos.CollectionDto": {"baseType": "Volo.Abp.Application.Dtos.EntityDto<System.Int32>", "isEnum": false, "enumNames": null, "enumValues": null, "genericArguments": null, "properties": [{"name": "ParentCollectionId", "jsonName": null, "type": "System.Int32?", "typeSimple": "number?", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "ContentCode", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "LanguageCode", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "ThumbnailFileId", "jsonName": null, "type": "System.Int32?", "typeSimple": "number?", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "ThumbnailBucketFile", "jsonName": null, "type": "HolyBless.Buckets.Dtos.BucketFileDto", "typeSimple": "HolyBless.Buckets.Dtos.BucketFileDto", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "Name", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "Description", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "Keywords", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "Views", "jsonName": null, "type": "System.Int32", "typeSimple": "number", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "<PERSON>s", "jsonName": null, "type": "System.Int32", "typeSimple": "number", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "ListStyle", "jsonName": null, "type": "HolyBless.Enums.ListStyle?", "typeSimple": "enum?", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "RenderAsOneSet", "jsonName": null, "type": "System.Boolean", "typeSimple": "boolean", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "Status", "jsonName": null, "type": "HolyBless.Enums.PublishStatus", "typeSimple": "enum", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "Memo", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "DefaultOrderBy", "jsonName": null, "type": "HolyBless.Enums.DefaultOrderByField", "typeSimple": "enum", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}]}, "HolyBless.Collections.Dtos.CollectionTreeDto": {"baseType": null, "isEnum": false, "enumNames": null, "enumValues": null, "genericArguments": null, "properties": [{"name": "Id", "jsonName": null, "type": "System.Int32", "typeSimple": "number", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "Name", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "ContentCode", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "ParentCollectionId", "jsonName": null, "type": "System.Int32?", "typeSimple": "number?", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "Description", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "Status", "jsonName": null, "type": "HolyBless.Enums.PublishStatus", "typeSimple": "enum", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "IsRoot", "jsonName": null, "type": "System.Boolean", "typeSimple": "boolean", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "Children", "jsonName": null, "type": "[HolyBless.Collections.Dtos.CollectionTreeDto]", "typeSimple": "[HolyBless.Collections.Dtos.CollectionTreeDto]", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}]}, "HolyBless.Enums.AlbumType": {"baseType": "System.Enum", "isEnum": true, "enumNames": ["Audio", "Video"], "enumValues": [0, 1], "genericArguments": null, "properties": null}, "HolyBless.Enums.ArticleContentCategory": {"baseType": "System.Enum", "isEnum": true, "enumNames": ["Lecture", "Article", "Remark", "StudentArticle", "Extract"], "enumValues": [0, 1, 2, 3, 4], "genericArguments": null, "properties": null}, "HolyBless.Enums.ChannelSource": {"baseType": "System.Enum", "isEnum": true, "enumNames": ["Unspefied", "Collection", "Ebook", "VirtualDisk", "PotCast"], "enumValues": [0, 1, 2, 3, 4], "genericArguments": null, "properties": null}, "HolyBless.Enums.ContentCategory": {"baseType": "System.Enum", "isEnum": true, "enumNames": ["<PERSON><PERSON><PERSON><PERSON>", "Image", "OriginalAudio", "OriginalVideo", "Document", "NonOriginalAudio", "NonOriginalVideo", "Package"], "enumValues": [0, 1, 2, 3, 4, 5, 6, 7], "genericArguments": null, "properties": null}, "HolyBless.Enums.DefaultOrderByField": {"baseType": "System.Enum", "isEnum": true, "enumNames": ["CreationTime", "LastModificationTime", "DeiveryDate"], "enumValues": [0, 1, 2], "genericArguments": null, "properties": null}, "HolyBless.Enums.ListStyle": {"baseType": "System.Enum", "isEnum": true, "enumNames": ["ImageCard", "SummaryCard", "ArticleTree", "CollectionTree", "CollectionArticleTree"], "enumValues": [0, 1, 8, 9, 10], "genericArguments": null, "properties": null}, "HolyBless.Enums.MediaType": {"baseType": "System.Enum", "isEnum": true, "enumNames": ["Image", "Audio", "Video", "Document", "Unknown"], "enumValues": [0, 1, 2, 3, 4], "genericArguments": null, "properties": null}, "HolyBless.Enums.PublishStatus": {"baseType": "System.Enum", "isEnum": true, "enumNames": ["Draft", "Published", "ContentReviewed"], "enumValues": [0, 1, 2], "genericArguments": null, "properties": null}, "HolyBless.Lookups.Dtos.CountryDto": {"baseType": "Volo.Abp.Application.Dtos.EntityDto<System.Int32>", "isEnum": false, "enumNames": null, "enumValues": null, "genericArguments": null, "properties": [{"name": "Name", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "Code", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "Code3", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "DefaultLangCode", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "DefaultSpokenLangCode", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}]}, "HolyBless.Results.ArticleAggregateResult": {"baseType": null, "isEnum": false, "enumNames": null, "enumValues": null, "genericArguments": null, "properties": [{"name": "Id", "jsonName": null, "type": "System.Int32", "typeSimple": "number", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "Title", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "Description", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "Keywords", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "Views", "jsonName": null, "type": "System.Int32", "typeSimple": "number", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "<PERSON>s", "jsonName": null, "type": "System.Int32", "typeSimple": "number", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "DeliveryDate", "jsonName": null, "type": "System.DateTime", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "ArticleContentCategory", "jsonName": null, "type": "HolyBless.Enums.ArticleContentCategory", "typeSimple": "enum", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "Status", "jsonName": null, "type": "HolyBless.Enums.PublishStatus", "typeSimple": "enum", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "Content", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "Memo", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "CreationTime", "jsonName": null, "type": "System.DateTime", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "LastModificationTime", "jsonName": null, "type": "System.DateTime?", "typeSimple": "string?", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "ThumbnailUrl", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "ArticleFiles", "jsonName": null, "type": "[HolyBless.Results.ArticleFileAggregateResult]", "typeSimple": "[HolyBless.Results.ArticleFileAggregateResult]", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}]}, "HolyBless.Results.ArticleFileAggregateResult": {"baseType": null, "isEnum": false, "enumNames": null, "enumValues": null, "genericArguments": null, "properties": [{"name": "Id", "jsonName": null, "type": "System.Int32", "typeSimple": "number", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "ArticleId", "jsonName": null, "type": "System.Int32", "typeSimple": "number", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "FileId", "jsonName": null, "type": "System.Int32", "typeSimple": "number", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "Title", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "Description", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "IsPrimary", "jsonName": null, "type": "System.Boolean", "typeSimple": "boolean", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "FileName", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "MediaType", "jsonName": null, "type": "HolyBless.Enums.MediaType", "typeSimple": "enum", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "ContentCategory", "jsonName": null, "type": "HolyBless.Enums.ContentCategory", "typeSimple": "enum", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "FileDeliveryDate", "jsonName": null, "type": "System.DateTime?", "typeSimple": "string?", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "FileViews", "jsonName": null, "type": "System.Int32", "typeSimple": "number", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "YoutubeId", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "FileUrl", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}]}, "HolyBless.Results.ArticleSummaryResult": {"baseType": null, "isEnum": false, "enumNames": null, "enumValues": null, "genericArguments": null, "properties": [{"name": "Id", "jsonName": null, "type": "System.Int32", "typeSimple": "number", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "ThumbnailUrl", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "Description", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "Title", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "CreationTime", "jsonName": null, "type": "System.DateTime", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "LastModificationTime", "jsonName": null, "type": "System.DateTime", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "DeliveryDate", "jsonName": null, "type": "System.DateTime", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}]}, "HolyBless.Results.CollectionSummaryRequest": {"baseType": null, "isEnum": false, "enumNames": null, "enumValues": null, "genericArguments": null, "properties": [{"name": "<PERSON><PERSON>", "jsonName": null, "type": "System.Int32", "typeSimple": "number", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "MaxResultCount", "jsonName": null, "type": "System.Int32", "typeSimple": "number", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "Sorting", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "Year", "jsonName": null, "type": "System.Int32?", "typeSimple": "number?", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "Month", "jsonName": null, "type": "System.Int32?", "typeSimple": "number?", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}]}, "HolyBless.Results.CollectionSummaryResult": {"baseType": null, "isEnum": false, "enumNames": null, "enumValues": null, "genericArguments": null, "properties": [{"name": "Id", "jsonName": null, "type": "System.Int32", "typeSimple": "number", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "Name", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "Description", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "ContentCode", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "OrderByField", "jsonName": null, "type": "HolyBless.Enums.DefaultOrderByField", "typeSimple": "enum", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "TotalRecords", "jsonName": null, "type": "System.Int32", "typeSimple": "number", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "Articles", "jsonName": null, "type": "[HolyBless.Results.ArticleSummaryResult]", "typeSimple": "[HolyBless.Results.ArticleSummaryResult]", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}]}, "HolyBless.Tags.Dtos.TagDto": {"baseType": "Volo.Abp.Application.Dtos.EntityDto<System.Int32>", "isEnum": false, "enumNames": null, "enumValues": null, "genericArguments": null, "properties": [{"name": "ContentCode", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "TagName", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "LanguageCode", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "Views", "jsonName": null, "type": "System.Int32", "typeSimple": "number", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}]}, "HolyBless.Tags.Dtos.TagSearchDto": {"baseType": "Volo.Abp.Application.Dtos.PagedAndSortedResultRequestDto", "isEnum": false, "enumNames": null, "enumValues": null, "genericArguments": null, "properties": [{"name": "TagName", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "ContentCode", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}]}, "HolyBless.VirtualFolders.VirtualFolderFileDto": {"baseType": null, "isEnum": false, "enumNames": null, "enumValues": null, "genericArguments": null, "properties": [{"name": "Id", "jsonName": null, "type": "System.Int32", "typeSimple": "number", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "FileName", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "Title", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}]}, "HolyBless.VirtualFolders.VirtualFolderTreeDto": {"baseType": null, "isEnum": false, "enumNames": null, "enumValues": null, "genericArguments": null, "properties": [{"name": "Id", "jsonName": null, "type": "System.Int32", "typeSimple": "number", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "FolderName", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "Children", "jsonName": null, "type": "[HolyBless.VirtualFolders.VirtualFolderTreeDto]", "typeSimple": "[HolyBless.VirtualFolders.VirtualFolderTreeDto]", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "BucketFiles", "jsonName": null, "type": "[HolyBless.VirtualFolders.VirtualFolderFileDto]", "typeSimple": "[HolyBless.VirtualFolders.VirtualFolderFileDto]", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}]}, "Microsoft.AspNetCore.Mvc.IActionResult": {"baseType": null, "isEnum": false, "enumNames": null, "enumValues": null, "genericArguments": null, "properties": []}, "System.Nullable<T0>": {"baseType": "System.ValueType", "isEnum": false, "enumNames": null, "enumValues": null, "genericArguments": ["T"], "properties": [{"name": "HasValue", "jsonName": null, "type": "System.Boolean", "typeSimple": "boolean", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "Value", "jsonName": null, "type": "T", "typeSimple": "T", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}]}, "Volo.Abp.Account.Web.Areas.Account.Controllers.Models.AbpLoginResult": {"baseType": null, "isEnum": false, "enumNames": null, "enumValues": null, "genericArguments": null, "properties": [{"name": "Result", "jsonName": null, "type": "Volo.Abp.Account.Web.Areas.Account.Controllers.Models.LoginResultType", "typeSimple": "enum", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "Description", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}]}, "Volo.Abp.Account.Web.Areas.Account.Controllers.Models.LoginResultType": {"baseType": "System.Enum", "isEnum": true, "enumNames": ["Success", "InvalidUserNameOrPassword", "NotAllowed", "LockedOut", "RequiresTwoFactor"], "enumValues": [1, 2, 3, 4, 5], "genericArguments": null, "properties": null}, "Volo.Abp.Account.Web.Areas.Account.Controllers.Models.UserLoginInfo": {"baseType": null, "isEnum": false, "enumNames": null, "enumValues": null, "genericArguments": null, "properties": [{"name": "UserNameOrEmailAddress", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": true, "minLength": 0, "maxLength": 255, "minimum": null, "maximum": null, "regex": null}, {"name": "Password", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": true, "minLength": 0, "maxLength": 32, "minimum": null, "maximum": null, "regex": null}, {"name": "RememberMe", "jsonName": null, "type": "System.Boolean", "typeSimple": "boolean", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}]}, "Volo.Abp.Application.Dtos.AuditedEntityDto<T0>": {"baseType": "Volo.Abp.Application.Dtos.CreationAuditedEntityDto<TPrimaryKey>", "isEnum": false, "enumNames": null, "enumValues": null, "genericArguments": ["TPrimaryKey"], "properties": [{"name": "LastModificationTime", "jsonName": null, "type": "System.DateTime?", "typeSimple": "string?", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "LastModifierId", "jsonName": null, "type": "System.Guid?", "typeSimple": "string?", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}]}, "Volo.Abp.Application.Dtos.CreationAuditedEntityDto<T0>": {"baseType": "Volo.Abp.Application.Dtos.EntityDto<TPrimaryKey>", "isEnum": false, "enumNames": null, "enumValues": null, "genericArguments": ["TPrimaryKey"], "properties": [{"name": "CreationTime", "jsonName": null, "type": "System.DateTime", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "CreatorId", "jsonName": null, "type": "System.Guid?", "typeSimple": "string?", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}]}, "Volo.Abp.Application.Dtos.EntityDto": {"baseType": null, "isEnum": false, "enumNames": null, "enumValues": null, "genericArguments": null, "properties": []}, "Volo.Abp.Application.Dtos.EntityDto<T0>": {"baseType": "Volo.Abp.Application.Dtos.EntityDto", "isEnum": false, "enumNames": null, "enumValues": null, "genericArguments": ["<PERSON><PERSON><PERSON>"], "properties": [{"name": "Id", "jsonName": null, "type": "<PERSON><PERSON><PERSON>", "typeSimple": "<PERSON><PERSON><PERSON>", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}]}, "Volo.Abp.Application.Dtos.LimitedResultRequestDto": {"baseType": null, "isEnum": false, "enumNames": null, "enumValues": null, "genericArguments": null, "properties": [{"name": "MaxResultCount", "jsonName": null, "type": "System.Int32", "typeSimple": "number", "isRequired": false, "minLength": null, "maxLength": null, "minimum": "1", "maximum": "2147483647", "regex": null}]}, "Volo.Abp.Application.Dtos.ListResultDto<T0>": {"baseType": null, "isEnum": false, "enumNames": null, "enumValues": null, "genericArguments": ["T"], "properties": [{"name": "Items", "jsonName": null, "type": "[T]", "typeSimple": "[T]", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}]}, "Volo.Abp.Application.Dtos.PagedAndSortedResultRequestDto": {"baseType": "Volo.Abp.Application.Dtos.PagedResultRequestDto", "isEnum": false, "enumNames": null, "enumValues": null, "genericArguments": null, "properties": [{"name": "Sorting", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}]}, "Volo.Abp.Application.Dtos.PagedResultDto<T0>": {"baseType": "Volo.Abp.Application.Dtos.ListResultDto<T>", "isEnum": false, "enumNames": null, "enumValues": null, "genericArguments": ["T"], "properties": [{"name": "TotalCount", "jsonName": null, "type": "System.Int64", "typeSimple": "number", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}]}, "Volo.Abp.Application.Dtos.PagedResultRequestDto": {"baseType": "Volo.Abp.Application.Dtos.LimitedResultRequestDto", "isEnum": false, "enumNames": null, "enumValues": null, "genericArguments": null, "properties": [{"name": "SkipCount", "jsonName": null, "type": "System.Int32", "typeSimple": "number", "isRequired": false, "minLength": null, "maxLength": null, "minimum": "0", "maximum": "2147483647", "regex": null}]}, "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ApplicationAuthConfigurationDto": {"baseType": null, "isEnum": false, "enumNames": null, "enumValues": null, "genericArguments": null, "properties": [{"name": "GrantedPolicies", "jsonName": null, "type": "{System.String:<PERSON>.Boolean}", "typeSimple": "{string:boolean}", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}]}, "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ApplicationConfigurationDto": {"baseType": null, "isEnum": false, "enumNames": null, "enumValues": null, "genericArguments": null, "properties": [{"name": "Localization", "jsonName": null, "type": "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ApplicationLocalizationConfigurationDto", "typeSimple": "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ApplicationLocalizationConfigurationDto", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "<PERSON><PERSON>", "jsonName": null, "type": "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ApplicationAuthConfigurationDto", "typeSimple": "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ApplicationAuthConfigurationDto", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "Setting", "jsonName": null, "type": "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ApplicationSettingConfigurationDto", "typeSimple": "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ApplicationSettingConfigurationDto", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "CurrentUser", "jsonName": null, "type": "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.CurrentUserDto", "typeSimple": "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.CurrentUserDto", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "Features", "jsonName": null, "type": "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ApplicationFeatureConfigurationDto", "typeSimple": "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ApplicationFeatureConfigurationDto", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "GlobalFeatures", "jsonName": null, "type": "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ApplicationGlobalFeatureConfigurationDto", "typeSimple": "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ApplicationGlobalFeatureConfigurationDto", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "MultiTenancy", "jsonName": null, "type": "Volo.Abp.AspNetCore.Mvc.MultiTenancy.MultiTenancyInfoDto", "typeSimple": "Volo.Abp.AspNetCore.Mvc.MultiTenancy.MultiTenancyInfoDto", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "CurrentTenant", "jsonName": null, "type": "Volo.Abp.AspNetCore.Mvc.MultiTenancy.CurrentTenantDto", "typeSimple": "Volo.Abp.AspNetCore.Mvc.MultiTenancy.CurrentTenantDto", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "Timing", "jsonName": null, "type": "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.TimingDto", "typeSimple": "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.TimingDto", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "Clock", "jsonName": null, "type": "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ClockDto", "typeSimple": "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ClockDto", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "ObjectExtensions", "jsonName": null, "type": "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ObjectExtending.ObjectExtensionsDto", "typeSimple": "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ObjectExtending.ObjectExtensionsDto", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "ExtraProperties", "jsonName": null, "type": "{System.String:System.Object}", "typeSimple": "{string:object}", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}]}, "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ApplicationConfigurationRequestOptions": {"baseType": null, "isEnum": false, "enumNames": null, "enumValues": null, "genericArguments": null, "properties": [{"name": "IncludeLocalizationResources", "jsonName": null, "type": "System.Boolean", "typeSimple": "boolean", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}]}, "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ApplicationFeatureConfigurationDto": {"baseType": null, "isEnum": false, "enumNames": null, "enumValues": null, "genericArguments": null, "properties": [{"name": "Values", "jsonName": null, "type": "{System.String:System.String}", "typeSimple": "{string:string}", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}]}, "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ApplicationGlobalFeatureConfigurationDto": {"baseType": null, "isEnum": false, "enumNames": null, "enumValues": null, "genericArguments": null, "properties": [{"name": "EnabledFeatures", "jsonName": null, "type": "[System.String]", "typeSimple": "[string]", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}]}, "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ApplicationLocalizationConfigurationDto": {"baseType": null, "isEnum": false, "enumNames": null, "enumValues": null, "genericArguments": null, "properties": [{"name": "Values", "jsonName": null, "type": "{System.String:System.Collections.Generic.Dictionary<System.String,System.String>}", "typeSimple": "{string:System.Collections.Generic.Dictionary<string,string>}", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "Resources", "jsonName": null, "type": "{System.String:Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ApplicationLocalizationResourceDto}", "typeSimple": "{string:Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ApplicationLocalizationResourceDto}", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "Languages", "jsonName": null, "type": "[Volo.Abp.Localization.LanguageInfo]", "typeSimple": "[Volo.Abp.Localization.LanguageInfo]", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "CurrentCulture", "jsonName": null, "type": "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.CurrentCultureDto", "typeSimple": "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.CurrentCultureDto", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "DefaultResourceName", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "LanguagesMap", "jsonName": null, "type": "{System.String:[Volo.Abp.NameValue]}", "typeSimple": "{string:[Volo.Abp.NameValue]}", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "LanguageFilesMap", "jsonName": null, "type": "{System.String:[Volo.Abp.NameValue]}", "typeSimple": "{string:[Volo.Abp.NameValue]}", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}]}, "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ApplicationLocalizationDto": {"baseType": null, "isEnum": false, "enumNames": null, "enumValues": null, "genericArguments": null, "properties": [{"name": "Resources", "jsonName": null, "type": "{System.String:Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ApplicationLocalizationResourceDto}", "typeSimple": "{string:Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ApplicationLocalizationResourceDto}", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "CurrentCulture", "jsonName": null, "type": "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.CurrentCultureDto", "typeSimple": "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.CurrentCultureDto", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}]}, "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ApplicationLocalizationRequestDto": {"baseType": null, "isEnum": false, "enumNames": null, "enumValues": null, "genericArguments": null, "properties": [{"name": "CultureName", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": true, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "OnlyDynamics", "jsonName": null, "type": "System.Boolean", "typeSimple": "boolean", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}]}, "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ApplicationLocalizationResourceDto": {"baseType": null, "isEnum": false, "enumNames": null, "enumValues": null, "genericArguments": null, "properties": [{"name": "Texts", "jsonName": null, "type": "{System.String:System.String}", "typeSimple": "{string:string}", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "BaseResources", "jsonName": null, "type": "[System.String]", "typeSimple": "[string]", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}]}, "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ApplicationSettingConfigurationDto": {"baseType": null, "isEnum": false, "enumNames": null, "enumValues": null, "genericArguments": null, "properties": [{"name": "Values", "jsonName": null, "type": "{System.String:System.String}", "typeSimple": "{string:string}", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}]}, "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ClockDto": {"baseType": null, "isEnum": false, "enumNames": null, "enumValues": null, "genericArguments": null, "properties": [{"name": "Kind", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}]}, "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.CurrentCultureDto": {"baseType": null, "isEnum": false, "enumNames": null, "enumValues": null, "genericArguments": null, "properties": [{"name": "DisplayName", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "EnglishName", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "ThreeLetterIsoLanguageName", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "TwoLetterIsoLanguageName", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "IsRightToLeft", "jsonName": null, "type": "System.Boolean", "typeSimple": "boolean", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "CultureName", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "Name", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "NativeName", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "DateTimeFormat", "jsonName": null, "type": "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.DateTimeFormatDto", "typeSimple": "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.DateTimeFormatDto", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}]}, "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.CurrentUserDto": {"baseType": null, "isEnum": false, "enumNames": null, "enumValues": null, "genericArguments": null, "properties": [{"name": "IsAuthenticated", "jsonName": null, "type": "System.Boolean", "typeSimple": "boolean", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "Id", "jsonName": null, "type": "System.Guid?", "typeSimple": "string?", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "TenantId", "jsonName": null, "type": "System.Guid?", "typeSimple": "string?", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "ImpersonatorUserId", "jsonName": null, "type": "System.Guid?", "typeSimple": "string?", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "ImpersonatorTenantId", "jsonName": null, "type": "System.Guid?", "typeSimple": "string?", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "ImpersonatorUserName", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "ImpersonatorTenantName", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "UserName", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "Name", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "SurName", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "Email", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "EmailVerified", "jsonName": null, "type": "System.Boolean", "typeSimple": "boolean", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "PhoneNumber", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "PhoneNumberVerified", "jsonName": null, "type": "System.Boolean", "typeSimple": "boolean", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "Roles", "jsonName": null, "type": "[System.String]", "typeSimple": "[string]", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "SessionId", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}]}, "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.DateTimeFormatDto": {"baseType": null, "isEnum": false, "enumNames": null, "enumValues": null, "genericArguments": null, "properties": [{"name": "CalendarAlgorithmType", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "DateTimeFormatLong", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "ShortDatePattern", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "FullDateTimePattern", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "DateSeparator", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "ShortTimePattern", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "LongTimePattern", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}]}, "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.IanaTimeZone": {"baseType": null, "isEnum": false, "enumNames": null, "enumValues": null, "genericArguments": null, "properties": [{"name": "TimeZoneName", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}]}, "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ObjectExtending.EntityExtensionDto": {"baseType": null, "isEnum": false, "enumNames": null, "enumValues": null, "genericArguments": null, "properties": [{"name": "Properties", "jsonName": null, "type": "{System.String:Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ObjectExtending.ExtensionPropertyDto}", "typeSimple": "{string:Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ObjectExtending.ExtensionPropertyDto}", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "Configuration", "jsonName": null, "type": "{System.String:System.Object}", "typeSimple": "{string:object}", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}]}, "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ObjectExtending.ExtensionEnumDto": {"baseType": null, "isEnum": false, "enumNames": null, "enumValues": null, "genericArguments": null, "properties": [{"name": "Fields", "jsonName": null, "type": "[Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ObjectExtending.ExtensionEnumFieldDto]", "typeSimple": "[Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ObjectExtending.ExtensionEnumFieldDto]", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "LocalizationResource", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}]}, "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ObjectExtending.ExtensionEnumFieldDto": {"baseType": null, "isEnum": false, "enumNames": null, "enumValues": null, "genericArguments": null, "properties": [{"name": "Name", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "Value", "jsonName": null, "type": "System.Object", "typeSimple": "object", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}]}, "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ObjectExtending.ExtensionPropertyApiCreateDto": {"baseType": null, "isEnum": false, "enumNames": null, "enumValues": null, "genericArguments": null, "properties": [{"name": "IsAvailable", "jsonName": null, "type": "System.Boolean", "typeSimple": "boolean", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}]}, "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ObjectExtending.ExtensionPropertyApiDto": {"baseType": null, "isEnum": false, "enumNames": null, "enumValues": null, "genericArguments": null, "properties": [{"name": "OnGet", "jsonName": null, "type": "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ObjectExtending.ExtensionPropertyApiGetDto", "typeSimple": "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ObjectExtending.ExtensionPropertyApiGetDto", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "OnCreate", "jsonName": null, "type": "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ObjectExtending.ExtensionPropertyApiCreateDto", "typeSimple": "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ObjectExtending.ExtensionPropertyApiCreateDto", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "OnUpdate", "jsonName": null, "type": "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ObjectExtending.ExtensionPropertyApiUpdateDto", "typeSimple": "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ObjectExtending.ExtensionPropertyApiUpdateDto", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}]}, "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ObjectExtending.ExtensionPropertyApiGetDto": {"baseType": null, "isEnum": false, "enumNames": null, "enumValues": null, "genericArguments": null, "properties": [{"name": "IsAvailable", "jsonName": null, "type": "System.Boolean", "typeSimple": "boolean", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}]}, "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ObjectExtending.ExtensionPropertyApiUpdateDto": {"baseType": null, "isEnum": false, "enumNames": null, "enumValues": null, "genericArguments": null, "properties": [{"name": "IsAvailable", "jsonName": null, "type": "System.Boolean", "typeSimple": "boolean", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}]}, "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ObjectExtending.ExtensionPropertyAttributeDto": {"baseType": null, "isEnum": false, "enumNames": null, "enumValues": null, "genericArguments": null, "properties": [{"name": "TypeSimple", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "Config", "jsonName": null, "type": "{System.String:System.Object}", "typeSimple": "{string:object}", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}]}, "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ObjectExtending.ExtensionPropertyDto": {"baseType": null, "isEnum": false, "enumNames": null, "enumValues": null, "genericArguments": null, "properties": [{"name": "Type", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "TypeSimple", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "DisplayName", "jsonName": null, "type": "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ObjectExtending.LocalizableStringDto", "typeSimple": "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ObjectExtending.LocalizableStringDto", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "Api", "jsonName": null, "type": "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ObjectExtending.ExtensionPropertyApiDto", "typeSimple": "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ObjectExtending.ExtensionPropertyApiDto", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "Ui", "jsonName": null, "type": "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ObjectExtending.ExtensionPropertyUiDto", "typeSimple": "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ObjectExtending.ExtensionPropertyUiDto", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "Policy", "jsonName": null, "type": "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ObjectExtending.ExtensionPropertyPolicyDto", "typeSimple": "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ObjectExtending.ExtensionPropertyPolicyDto", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "Attributes", "jsonName": null, "type": "[Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ObjectExtending.ExtensionPropertyAttributeDto]", "typeSimple": "[Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ObjectExtending.ExtensionPropertyAttributeDto]", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "Configuration", "jsonName": null, "type": "{System.String:System.Object}", "typeSimple": "{string:object}", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "DefaultValue", "jsonName": null, "type": "System.Object", "typeSimple": "object", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}]}, "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ObjectExtending.ExtensionPropertyFeaturePolicyDto": {"baseType": null, "isEnum": false, "enumNames": null, "enumValues": null, "genericArguments": null, "properties": [{"name": "Features", "jsonName": null, "type": "[System.String]", "typeSimple": "[string]", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "RequiresAll", "jsonName": null, "type": "System.Boolean", "typeSimple": "boolean", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}]}, "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ObjectExtending.ExtensionPropertyGlobalFeaturePolicyDto": {"baseType": null, "isEnum": false, "enumNames": null, "enumValues": null, "genericArguments": null, "properties": [{"name": "Features", "jsonName": null, "type": "[System.String]", "typeSimple": "[string]", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "RequiresAll", "jsonName": null, "type": "System.Boolean", "typeSimple": "boolean", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}]}, "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ObjectExtending.ExtensionPropertyPermissionPolicyDto": {"baseType": null, "isEnum": false, "enumNames": null, "enumValues": null, "genericArguments": null, "properties": [{"name": "PermissionNames", "jsonName": null, "type": "[System.String]", "typeSimple": "[string]", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "RequiresAll", "jsonName": null, "type": "System.Boolean", "typeSimple": "boolean", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}]}, "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ObjectExtending.ExtensionPropertyPolicyDto": {"baseType": null, "isEnum": false, "enumNames": null, "enumValues": null, "genericArguments": null, "properties": [{"name": "GlobalFeatures", "jsonName": null, "type": "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ObjectExtending.ExtensionPropertyGlobalFeaturePolicyDto", "typeSimple": "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ObjectExtending.ExtensionPropertyGlobalFeaturePolicyDto", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "Features", "jsonName": null, "type": "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ObjectExtending.ExtensionPropertyFeaturePolicyDto", "typeSimple": "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ObjectExtending.ExtensionPropertyFeaturePolicyDto", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "Permissions", "jsonName": null, "type": "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ObjectExtending.ExtensionPropertyPermissionPolicyDto", "typeSimple": "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ObjectExtending.ExtensionPropertyPermissionPolicyDto", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}]}, "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ObjectExtending.ExtensionPropertyUiDto": {"baseType": null, "isEnum": false, "enumNames": null, "enumValues": null, "genericArguments": null, "properties": [{"name": "OnTable", "jsonName": null, "type": "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ObjectExtending.ExtensionPropertyUiTableDto", "typeSimple": "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ObjectExtending.ExtensionPropertyUiTableDto", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "OnCreateForm", "jsonName": null, "type": "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ObjectExtending.ExtensionPropertyUiFormDto", "typeSimple": "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ObjectExtending.ExtensionPropertyUiFormDto", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "OnEditForm", "jsonName": null, "type": "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ObjectExtending.ExtensionPropertyUiFormDto", "typeSimple": "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ObjectExtending.ExtensionPropertyUiFormDto", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "Lookup", "jsonName": null, "type": "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ObjectExtending.ExtensionPropertyUiLookupDto", "typeSimple": "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ObjectExtending.ExtensionPropertyUiLookupDto", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}]}, "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ObjectExtending.ExtensionPropertyUiFormDto": {"baseType": null, "isEnum": false, "enumNames": null, "enumValues": null, "genericArguments": null, "properties": [{"name": "IsVisible", "jsonName": null, "type": "System.Boolean", "typeSimple": "boolean", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}]}, "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ObjectExtending.ExtensionPropertyUiLookupDto": {"baseType": null, "isEnum": false, "enumNames": null, "enumValues": null, "genericArguments": null, "properties": [{"name": "Url", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "ResultListPropertyName", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "DisplayPropertyName", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "ValuePropertyName", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "FilterParamName", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}]}, "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ObjectExtending.ExtensionPropertyUiTableDto": {"baseType": null, "isEnum": false, "enumNames": null, "enumValues": null, "genericArguments": null, "properties": [{"name": "IsVisible", "jsonName": null, "type": "System.Boolean", "typeSimple": "boolean", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}]}, "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ObjectExtending.LocalizableStringDto": {"baseType": null, "isEnum": false, "enumNames": null, "enumValues": null, "genericArguments": null, "properties": [{"name": "Name", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "Resource", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}]}, "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ObjectExtending.ModuleExtensionDto": {"baseType": null, "isEnum": false, "enumNames": null, "enumValues": null, "genericArguments": null, "properties": [{"name": "Entities", "jsonName": null, "type": "{System.String:Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ObjectExtending.EntityExtensionDto}", "typeSimple": "{string:Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ObjectExtending.EntityExtensionDto}", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "Configuration", "jsonName": null, "type": "{System.String:System.Object}", "typeSimple": "{string:object}", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}]}, "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ObjectExtending.ObjectExtensionsDto": {"baseType": null, "isEnum": false, "enumNames": null, "enumValues": null, "genericArguments": null, "properties": [{"name": "<PERSON><PERSON><PERSON>", "jsonName": null, "type": "{System.String:Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ObjectExtending.ModuleExtensionDto}", "typeSimple": "{string:Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ObjectExtending.ModuleExtensionDto}", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "Enums", "jsonName": null, "type": "{System.String:Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ObjectExtending.ExtensionEnumDto}", "typeSimple": "{string:Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ObjectExtending.ExtensionEnumDto}", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}]}, "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.TimeZone": {"baseType": null, "isEnum": false, "enumNames": null, "enumValues": null, "genericArguments": null, "properties": [{"name": "<PERSON><PERSON>", "jsonName": null, "type": "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.IanaTimeZone", "typeSimple": "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.IanaTimeZone", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "Windows", "jsonName": null, "type": "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.WindowsTimeZone", "typeSimple": "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.WindowsTimeZone", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}]}, "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.TimingDto": {"baseType": null, "isEnum": false, "enumNames": null, "enumValues": null, "genericArguments": null, "properties": [{"name": "TimeZone", "jsonName": null, "type": "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.TimeZone", "typeSimple": "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.TimeZone", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}]}, "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.WindowsTimeZone": {"baseType": null, "isEnum": false, "enumNames": null, "enumValues": null, "genericArguments": null, "properties": [{"name": "TimeZoneId", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}]}, "Volo.Abp.AspNetCore.Mvc.MultiTenancy.CurrentTenantDto": {"baseType": null, "isEnum": false, "enumNames": null, "enumValues": null, "genericArguments": null, "properties": [{"name": "Id", "jsonName": null, "type": "System.Guid?", "typeSimple": "string?", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "Name", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "IsAvailable", "jsonName": null, "type": "System.Boolean", "typeSimple": "boolean", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}]}, "Volo.Abp.AspNetCore.Mvc.MultiTenancy.FindTenantResultDto": {"baseType": null, "isEnum": false, "enumNames": null, "enumValues": null, "genericArguments": null, "properties": [{"name": "Success", "jsonName": null, "type": "System.Boolean", "typeSimple": "boolean", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "TenantId", "jsonName": null, "type": "System.Guid?", "typeSimple": "string?", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "Name", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "NormalizedName", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "IsActive", "jsonName": null, "type": "System.Boolean", "typeSimple": "boolean", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}]}, "Volo.Abp.AspNetCore.Mvc.MultiTenancy.MultiTenancyInfoDto": {"baseType": null, "isEnum": false, "enumNames": null, "enumValues": null, "genericArguments": null, "properties": [{"name": "IsEnabled", "jsonName": null, "type": "System.Boolean", "typeSimple": "boolean", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}]}, "Volo.Abp.FeatureManagement.FeatureDto": {"baseType": null, "isEnum": false, "enumNames": null, "enumValues": null, "genericArguments": null, "properties": [{"name": "Name", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "DisplayName", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "Value", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "Provider", "jsonName": null, "type": "Volo.Abp.FeatureManagement.FeatureProviderDto", "typeSimple": "Volo.Abp.FeatureManagement.FeatureProviderDto", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "Description", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "ValueType", "jsonName": null, "type": "Volo.Abp.Validation.StringValues.IStringValueType", "typeSimple": "Volo.Abp.Validation.StringValues.IStringValueType", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "De<PERSON><PERSON>", "jsonName": null, "type": "System.Int32", "typeSimple": "number", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "ParentName", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}]}, "Volo.Abp.FeatureManagement.FeatureGroupDto": {"baseType": null, "isEnum": false, "enumNames": null, "enumValues": null, "genericArguments": null, "properties": [{"name": "Name", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "DisplayName", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "Features", "jsonName": null, "type": "[Volo.Abp.FeatureManagement.FeatureDto]", "typeSimple": "[Volo.Abp.FeatureManagement.FeatureDto]", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}]}, "Volo.Abp.FeatureManagement.FeatureProviderDto": {"baseType": null, "isEnum": false, "enumNames": null, "enumValues": null, "genericArguments": null, "properties": [{"name": "Name", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "Key", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}]}, "Volo.Abp.FeatureManagement.GetFeatureListResultDto": {"baseType": null, "isEnum": false, "enumNames": null, "enumValues": null, "genericArguments": null, "properties": [{"name": "Groups", "jsonName": null, "type": "[Volo.Abp.FeatureManagement.FeatureGroupDto]", "typeSimple": "[Volo.Abp.FeatureManagement.FeatureGroupDto]", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}]}, "Volo.Abp.FeatureManagement.UpdateFeatureDto": {"baseType": null, "isEnum": false, "enumNames": null, "enumValues": null, "genericArguments": null, "properties": [{"name": "Name", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "Value", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}]}, "Volo.Abp.FeatureManagement.UpdateFeaturesDto": {"baseType": null, "isEnum": false, "enumNames": null, "enumValues": null, "genericArguments": null, "properties": [{"name": "Features", "jsonName": null, "type": "[Volo.Abp.FeatureManagement.UpdateFeatureDto]", "typeSimple": "[Volo.Abp.FeatureManagement.UpdateFeatureDto]", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}]}, "Volo.Abp.Http.Modeling.ActionApiDescriptionModel": {"baseType": null, "isEnum": false, "enumNames": null, "enumValues": null, "genericArguments": null, "properties": [{"name": "UniqueName", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "Name", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "HttpMethod", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "Url", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "SupportedVersions", "jsonName": null, "type": "[System.String]", "typeSimple": "[string]", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "ParametersOnMethod", "jsonName": null, "type": "[Volo.Abp.Http.Modeling.MethodParameterApiDescriptionModel]", "typeSimple": "[Volo.Abp.Http.Modeling.MethodParameterApiDescriptionModel]", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "Parameters", "jsonName": null, "type": "[Volo.Abp.Http.Modeling.ParameterApiDescriptionModel]", "typeSimple": "[Volo.Abp.Http.Modeling.ParameterApiDescriptionModel]", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "ReturnValue", "jsonName": null, "type": "Volo.Abp.Http.Modeling.ReturnValueApiDescriptionModel", "typeSimple": "Volo.Abp.Http.Modeling.ReturnValueApiDescriptionModel", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "AllowAnonymous", "jsonName": null, "type": "System.Boolean?", "typeSimple": "boolean?", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "ImplementFrom", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}]}, "Volo.Abp.Http.Modeling.ApplicationApiDescriptionModel": {"baseType": null, "isEnum": false, "enumNames": null, "enumValues": null, "genericArguments": null, "properties": [{"name": "<PERSON><PERSON><PERSON>", "jsonName": null, "type": "{System.String:Volo.Abp.Http.Modeling.ModuleApiDescriptionModel}", "typeSimple": "{string:Volo.Abp.Http.Modeling.ModuleApiDescriptionModel}", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "Types", "jsonName": null, "type": "{System.String:Volo.Abp.Http.Modeling.TypeApiDescriptionModel}", "typeSimple": "{string:Volo.Abp.Http.Modeling.TypeApiDescriptionModel}", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}]}, "Volo.Abp.Http.Modeling.ApplicationApiDescriptionModelRequestDto": {"baseType": null, "isEnum": false, "enumNames": null, "enumValues": null, "genericArguments": null, "properties": [{"name": "IncludeTypes", "jsonName": null, "type": "System.Boolean", "typeSimple": "boolean", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}]}, "Volo.Abp.Http.Modeling.ControllerApiDescriptionModel": {"baseType": null, "isEnum": false, "enumNames": null, "enumValues": null, "genericArguments": null, "properties": [{"name": "ControllerName", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "ControllerGroupName", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "IsRemoteService", "jsonName": null, "type": "System.Boolean", "typeSimple": "boolean", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "IsIntegrationService", "jsonName": null, "type": "System.Boolean", "typeSimple": "boolean", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "ApiVersion", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "Type", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "Interfaces", "jsonName": null, "type": "[Volo.Abp.Http.Modeling.ControllerInterfaceApiDescriptionModel]", "typeSimple": "[Volo.Abp.Http.Modeling.ControllerInterfaceApiDescriptionModel]", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "Actions", "jsonName": null, "type": "{System.String:Volo.Abp.Http.Modeling.ActionApiDescriptionModel}", "typeSimple": "{string:Volo.Abp.Http.Modeling.ActionApiDescriptionModel}", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}]}, "Volo.Abp.Http.Modeling.ControllerInterfaceApiDescriptionModel": {"baseType": null, "isEnum": false, "enumNames": null, "enumValues": null, "genericArguments": null, "properties": [{"name": "Type", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "Name", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "Methods", "jsonName": null, "type": "[Volo.Abp.Http.Modeling.InterfaceMethodApiDescriptionModel]", "typeSimple": "[Volo.Abp.Http.Modeling.InterfaceMethodApiDescriptionModel]", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}]}, "Volo.Abp.Http.Modeling.InterfaceMethodApiDescriptionModel": {"baseType": null, "isEnum": false, "enumNames": null, "enumValues": null, "genericArguments": null, "properties": [{"name": "Name", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "ParametersOnMethod", "jsonName": null, "type": "[Volo.Abp.Http.Modeling.MethodParameterApiDescriptionModel]", "typeSimple": "[Volo.Abp.Http.Modeling.MethodParameterApiDescriptionModel]", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "ReturnValue", "jsonName": null, "type": "Volo.Abp.Http.Modeling.ReturnValueApiDescriptionModel", "typeSimple": "Volo.Abp.Http.Modeling.ReturnValueApiDescriptionModel", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}]}, "Volo.Abp.Http.Modeling.MethodParameterApiDescriptionModel": {"baseType": null, "isEnum": false, "enumNames": null, "enumValues": null, "genericArguments": null, "properties": [{"name": "Name", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "TypeAsString", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "Type", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "TypeSimple", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "IsOptional", "jsonName": null, "type": "System.Boolean", "typeSimple": "boolean", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "DefaultValue", "jsonName": null, "type": "System.Object", "typeSimple": "object", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}]}, "Volo.Abp.Http.Modeling.ModuleApiDescriptionModel": {"baseType": null, "isEnum": false, "enumNames": null, "enumValues": null, "genericArguments": null, "properties": [{"name": "RootPath", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "RemoteServiceName", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "Controllers", "jsonName": null, "type": "{System.String:Volo.Abp.Http.Modeling.ControllerApiDescriptionModel}", "typeSimple": "{string:Volo.Abp.Http.Modeling.ControllerApiDescriptionModel}", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}]}, "Volo.Abp.Http.Modeling.ParameterApiDescriptionModel": {"baseType": null, "isEnum": false, "enumNames": null, "enumValues": null, "genericArguments": null, "properties": [{"name": "NameOnMethod", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "Name", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "JsonName", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "Type", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "TypeSimple", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "IsOptional", "jsonName": null, "type": "System.Boolean", "typeSimple": "boolean", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "DefaultValue", "jsonName": null, "type": "System.Object", "typeSimple": "object", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "ConstraintTypes", "jsonName": null, "type": "[System.String]", "typeSimple": "[string]", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "BindingSourceId", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "DescriptorName", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}]}, "Volo.Abp.Http.Modeling.PropertyApiDescriptionModel": {"baseType": null, "isEnum": false, "enumNames": null, "enumValues": null, "genericArguments": null, "properties": [{"name": "Name", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "JsonName", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "Type", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "TypeSimple", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "IsRequired", "jsonName": null, "type": "System.Boolean", "typeSimple": "boolean", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "jsonName": null, "type": "System.Int32?", "typeSimple": "number?", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "jsonName": null, "type": "System.Int32?", "typeSimple": "number?", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "Minimum", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "Maximum", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "Regex", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}]}, "Volo.Abp.Http.Modeling.ReturnValueApiDescriptionModel": {"baseType": null, "isEnum": false, "enumNames": null, "enumValues": null, "genericArguments": null, "properties": [{"name": "Type", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "TypeSimple", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}]}, "Volo.Abp.Http.Modeling.TypeApiDescriptionModel": {"baseType": null, "isEnum": false, "enumNames": null, "enumValues": null, "genericArguments": null, "properties": [{"name": "BaseType", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "IsEnum", "jsonName": null, "type": "System.Boolean", "typeSimple": "boolean", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "EnumNames", "jsonName": null, "type": "[System.String]", "typeSimple": "[string]", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "jsonName": null, "type": "[System.Object]", "typeSimple": "[object]", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "GenericArguments", "jsonName": null, "type": "[System.String]", "typeSimple": "[string]", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "Properties", "jsonName": null, "type": "[Volo.Abp.Http.Modeling.PropertyApiDescriptionModel]", "typeSimple": "[Volo.Abp.Http.Modeling.PropertyApiDescriptionModel]", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}]}, "Volo.Abp.Localization.LanguageInfo": {"baseType": null, "isEnum": false, "enumNames": null, "enumValues": null, "genericArguments": null, "properties": [{"name": "CultureName", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "UiCultureName", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "DisplayName", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "TwoLetterISOLanguageName", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}]}, "Volo.Abp.NameValue": {"baseType": "Volo.Abp.NameValue<System.String>", "isEnum": false, "enumNames": null, "enumValues": null, "genericArguments": null, "properties": []}, "Volo.Abp.NameValue<T0>": {"baseType": null, "isEnum": false, "enumNames": null, "enumValues": null, "genericArguments": ["T"], "properties": [{"name": "Name", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "Value", "jsonName": null, "type": "T", "typeSimple": "T", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}]}, "Volo.Abp.Validation.StringValues.IStringValueType": {"baseType": null, "isEnum": false, "enumNames": null, "enumValues": null, "genericArguments": null, "properties": [{"name": "Name", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "<PERSON><PERSON>", "jsonName": null, "type": "System.Object", "typeSimple": "object", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "Properties", "jsonName": null, "type": "{System.String:System.Object}", "typeSimple": "{string:object}", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "Validator", "jsonName": null, "type": "Volo.Abp.Validation.StringValues.IValueValidator", "typeSimple": "Volo.Abp.Validation.StringValues.IValueValidator", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}]}, "Volo.Abp.Validation.StringValues.IValueValidator": {"baseType": null, "isEnum": false, "enumNames": null, "enumValues": null, "genericArguments": null, "properties": [{"name": "Name", "jsonName": null, "type": "System.String", "typeSimple": "string", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "<PERSON><PERSON>", "jsonName": null, "type": "System.Object", "typeSimple": "object", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}, {"name": "Properties", "jsonName": null, "type": "{System.String:System.Object}", "typeSimple": "{string:object}", "isRequired": false, "minLength": null, "maxLength": null, "minimum": null, "maximum": null, "regex": null}]}}}