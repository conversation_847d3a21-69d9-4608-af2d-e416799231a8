<div class="summary-cards-container p-6">
  <!-- 卡片网格容器 -->
  <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
    @for (item of cardItems; track item.id) {
      <p-card styleClass="card-item" [id]="item.id">
        <p
          class="p-card-title cursor-pointer"
          (click)="navigateToArticle(item.id)"
        >
          {{ item.title }}
        </p>
        <p class="card-content mt-2">
          {{ item.description }}
        </p>
        <ng-template pTemplate="footer">
          <div class="text-gray-500 text-sm flex items-center">
            {{ item.creationTime | date: "yyyy-MM-dd HH:mm:ss" }}
          </div>
        </ng-template>
      </p-card>
    }
  </div>

  <!-- 分页组件 -->
  <div class="pagination-container">
    <p-paginator
      [first]="first"
      [rows]="rows"
      [totalRecords]="totalRecords"
      [rowsPerPageOptions]="rowsPerPageOptions"
      [showPageLinks]="!isMobile"
      [showCurrentPageReport]="isMobile"
      (onPageChange)="onPageChange($event)"
      styleClass="custom-paginator"
    >
    </p-paginator>
  </div>
</div>
